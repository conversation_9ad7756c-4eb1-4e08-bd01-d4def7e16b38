# Agente IA Síndico com MCP + WhatsApp

Este guia explica como usar os dois fluxos fornecidos:
- agente_ia_sindico_mcp.json: workflow principal (webhook → autorização → contexto IA → AI Agent DeepSeek com memória Redis → parse → WAHA envio).
- mcp_sindico.json: servidor MCP com ferramentas (HTTP/Google/Postgres) expostas ao agente via MCP Client.

## Requisitos
- n8n v1.73+ (ou superior com nodes LangChain/MCP).
- Credenciais:
  - DeepSeek API Key.
  - Redis (host, porta, senha): para memória de conversa por telefone.
  - WAHA (WhatsApp HTTP API) baseURL e token.
  - Postgres/Supabase (host, db, user, pass, ssl). Tabela `allowed_phones(phone, nome, unidade, status)`.
  - (Opcional) Google Calendar/Drive credenciais.

## Como importar
1. Em n8n, Importar Workflow e selecione ambos os arquivos JSON.
2. Verifique se os nodes aparecem sem erro de tipo/versão. Se precisar, ajuste `typeVersion` conforme sua versão n8n.

## Configuração por nó
- DeepSeek Chat Model: associe a credencial "DeepSeek account". Escolha o modelo (p. ex. `deepseek-chat`), temperatura baixa.
- Redis Chat Memory: defina a credencial Redis. SessionKey já usa `{{$json.normPhone}}` (um número por conversa).
- Webhook Entrada1: anote a URL de teste/produção. O parse extrai `normPhone` e `messageText` de webhooks WAHA/baileys/evolution comuns.
- Postgres Check Allowlist: ajuste a credencial e confirme nome da tabela/colunas. `continueIfEmpty` mantido para não quebrar o fluxo.
- IF Autorizado?1: quando false, envia imediatamente `reply_text` para WhatsApp; quando true, segue para IA.
- Preparar Contexto IA1: constrói `prompt_treinado` e contexto/intenções.
- AI Agent: recebe o prompt e usa o DeepSeek + Memória + MCP Client Tools.
- Parse Resposta IA: extrai `{acao, params, resposta_usuario}` da saída do modelo (inclusive de blocos ```json). Gera `reply_text`.
- WAHA Send a text message: chatId = `{{$json.normPhone}}@c.us`, texto = `{{$json.reply_text}}`.
- MCP Server (mcp_sindico.json): expõe ferramentas como "API Agendar Evento", "API Abrir Ticket", etc., com `toolDescription` para descoberta pelo agente.

## MCP: como usar
- Ative o fluxo `MCP Server Trigger` (mcp_sindico.json). Isso expõe ferramentas MCP em uma rota interna do n8n.
- No workflow principal, o node `MCP Client` torna essas ferramentas disponíveis ao `AI Agent` como ferramentas invocáveis durante o raciocínio.
- Você pode começar sem configurar as credenciais HTTP/Google: o agente listará as ferramentas, mas chamadas reais falharão até configurar os endpoints. Sugestão: deixe endpoints locais mockados (ex.: http://localhost:3000/mock/agendar).

## WhatsApp (WAHA)
- Configure a credencial no node WAHA. Garanta que a instância esteja ligada e autenticada com QR.
- chatId: use DDI+DDD+número, ex.: <EMAIL>.
- Dica: crie um webhook WAHA → n8n Webhook Entrada1 para fechar o ciclo.

## Postgres / Supabase
- Tabela exemplo:
  create table allowed_phones (
    phone text primary key,
    nome text,
    unidade text,
    status text check (status in ('approved','pending','blocked'))
  );
- Aprove o número do morador definindo status='approved'.

Para cadastro via e-mail com aprovação na caixa postal do síndico, veja também `ONBOARDING_EMAIL.md` (contém o padrão de e-mail, parsing e fluxo de aprovação por reply/links).

## Ajustes e extensões
- Intenções: edite a lista em `Preparar Contexto IA1`.
- Ações automáticas: conecte `Parse Resposta IA` → Switch (não incluído aqui) para acionar APIs específicas, se desejar.
- Logs: habilite Console em nodes Code para depurar parsing.

## Teste rápido
1. Dispare Webhook com body contendo `from` e `text`.
2. Com número não aprovado: deve responder com mensagem de cadastro via WAHA.
3. Com número aprovado: IA responde, `Parse Resposta IA` cria `reply_text`, WAHA envia.

## Problemas comuns
- Import falha por typeVersion: ajuste para valores suportados na sua versão.
- WAHA não envia: verifique credencial/instância e formatação do chatId.
- Redis não conecta: confirme host/porta/SSL.
- IA responde sem JSON: `Parse Resposta IA` cobre, porém refine o prompt em `Preparar Contexto IA1` com exemplos.

## Segurança
- Não exponha o webhook sem verificação. Considere validar assinatura do provedor WA.
- Filtre comandos administrativos por allowlist.
