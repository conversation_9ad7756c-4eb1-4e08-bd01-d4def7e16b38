{"name": "Mcp_sindico", "nodes": [{"parameters": {"path": "e5e6c519-5447-4d59-aea1-43fa11cf9640"}, "type": "@n8n/n8n-nodes-langchain.mcpTrigger", "typeVersion": 2, "position": [-32, -96], "id": "6d03c539-6aaf-499b-ab02-8888676df753", "name": "MCP Server Trigger", "webhookId": "e5e6c519-5447-4d59-aea1-43fa11cf9640"}, {"parameters": {"toolDescription": "Endpoint interno para agendar áreas comuns (salão, churrasqueira, etc). Envie JSON com titulo, data, horario, local.", "method": "POST", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-272, 208], "id": "72724c38-4aae-4560-9730-77ab641a146d", "name": "API Agendar Evento"}, {"parameters": {"toolDescription": "Endpoint alternativo para agendamentos (homolog/backup).", "method": "POST", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-128, 208], "id": "0d46e613-b8c3-4c30-9ebd-70d10ae6782e", "name": "API Agendar Evento1"}, {"parameters": {"toolDescription": "Abrir ticket de manutenção com categoria e descrição.", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [0, 208], "id": "1b07b555-1c35-47a1-9f62-1b8c648bc4ef", "name": "API Abrir Ticket"}, {"parameters": {"toolDescription": "Gerar avisos de vencimento e notificações em massa.", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [160, 208], "id": "ed4ba034-f14f-427c-a7a1-fb097947c2cc", "name": "API Preparar Avisos"}, {"parameters": {"calendar": {"__rl": true, "mode": "list", "value": ""}, "toolDescription": "Criar evento no Google Calendar (exige credencial).", "additionalFields": {}}, "type": "n8n-nodes-base.googleCalendarTool", "typeVersion": 1.3, "position": [320, 192], "id": "03a0b370-15d5-493a-9d26-6954d5c15c3b", "name": "Create an event in Google Calendar"}, {"parameters": {"driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "mode": "list", "value": "root", "cachedResultName": "/ (Root folder)"}, "options": {}, "toolDescription": "Enviar arquivo para pasta do Google Drive."}, "type": "n8n-nodes-base.googleDriveTool", "typeVersion": 3, "position": [464, -32], "id": "fa6d8805-4198-4bd4-9acf-b61832abf140", "name": "Upload file in Google Drive"}, {"parameters": {"schema": {"__rl": true, "mode": "list", "value": "public"}, "table": {"__rl": true, "mode": "list", "value": ""}, "toolDescription": "Inserir linhas em tabela Postgres (Supabase). Informe schema, tabela e payload."}, "type": "n8n-nodes-base.postgresTool", "typeVersion": 2.6, "position": [544, 192], "id": "8fc30cf9-bdc5-4d48-883d-08fe1892396c", "name": "Insert rows in a table in Postgres", "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"toolDescription": "Fornecer extratos de contas de condomínio para moradores individuais, como valores pagos ou pendentes (com integração a sistemas financeiros, garantindo privacidade via autenticação).", "method": "POST", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-496, 320], "id": "acced08d-52f3-4c98-999f-1b076b960e57", "name": "Consultar Extrato"}, {"parameters": {"toolDescription": "Compilar dados sobre despesas, receitas ou ocorrências mensais para o síndico revisar, facilitando a prestação de contas", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-512, 128], "id": "327dc145-3e2b-4ded-adc2-16ad4d74e930", "name": "<PERSON><PERSON><PERSON>"}, {"parameters": {"toolDescription": "Orientar em situações como incêndios ou quedas de energia, fornecendo instruções básicas e chamando serviços de emergência se necessário.", "options": {}}, "type": "n8n-nodes-base.httpRequestTool", "typeVersion": 4.2, "position": [-560, 0], "id": "7fe06ea1-8da9-4c86-bfb3-768ffdbb3082", "name": "Suporte emergencial"}], "pinData": {}, "connections": {"API Agendar Evento": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "API Agendar Evento1": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "API Abrir Ticket": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "API Preparar Avisos": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Create an event in Google Calendar": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Upload file in Google Drive": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Insert rows in a table in Postgres": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Consultar Extrato": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Gerar relatórios": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}, "Suporte emergencial": {"ai_tool": [[{"node": "MCP Server Trigger", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1beff01c-efa8-4a33-847f-9532899e55d1", "meta": {"instanceId": "26bb5e00e3b41650f1c80a17ba0b2bc2c2393bb92f9a42c3ffc7c225c9d35205"}, "id": "QURwmZgRJHaDNoSo", "tags": []}