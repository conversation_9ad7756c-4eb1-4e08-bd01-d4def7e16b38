{"name": "Cadastro_SindicoAi", "nodes": [{"parameters": {"httpMethod": "POST", "path": "sindico-decisao", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1968, -288], "id": "571e22db-b928-42aa-b7bb-e2bf12ab8180", "name": "Webhook - Mensagens WhatsApp", "webhookId": "30731c76-d12f-4bb6-b5ff-ef15846f40c5"}, {"parameters": {"jsCode": "// Parse mensagem recebida do WhatsApp - VERSÃO UNIFICADA\n// 1) Normalizar telefone\nfunction normPhone(input) {\n  if (!input) return '';\n  let digits = String(input).replace(/[^0-9]/g, '');\n  if (digits.startsWith('55') && digits.length > 11) digits = digits.slice(-11);\n  if ((digits.length === 10 || digits.length === 11) && !digits.startsWith('55')) digits = '55' + digits;\n  return digits;\n}\n\n// 2) Extrair campos do payload do WAHA em diferentes formatos\nlet text = '';\nlet from = '';\nlet senderName = 'Usuário';\nconst jsonData = typeof $json === 'object' && $json !== null ? $json : {};\nif (jsonData.text && jsonData.from) {\n  text = jsonData.text; from = jsonData.from; senderName = jsonData.pushName || jsonData.notifyName || senderName;\n} else if (jsonData.body && jsonData.body.message) {\n  const m = jsonData.body.message; text = m.text || m.body || ''; from = m.from || m.chatId || ''; senderName = m.notifyName || m.pushName || senderName;\n} else if (jsonData.body) {\n  const b = jsonData.body; text = b.text || b.body || b.message || ''; from = b.from || b.chatId || ''; senderName = b.notifyName || b.pushName || senderName;\n} else {\n  text = jsonData.message || jsonData.text || jsonData.body || ''; from = jsonData.from || jsonData.chatId || ''; senderName = jsonData.pushName || jsonData.notifyName || senderName;\n}\n\nconst senderPhone = normPhone(String(from).replace('@c.us',''));\nconst messageText = String(text || '').trim();\nconst lowerText = messageText.toLowerCase();\n\n// 3) Flags booleanas para o Switch\nconst sindicoNumber = '5522997986724';\nconst isSindicoDecision = senderPhone === sindicoNumber && (/^APROVAR\\b|^REJEITAR\\b/i).test(messageText);\nconst shouldIgnore = senderPhone === sindicoNumber && !isSindicoDecision;\nconst hasNome = /nome\\s*[:|-]\\s*([^\\n]+)/i.test(messageText);\nconst hasUnidade = /(?:unidade|apto)\\s*[:|-]\\s*([^\\n]+)/i.test(messageText);\nconst hasEmail = /[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}/i.test(messageText);\nconst isCompleteData = !shouldIgnore && !isSindicoDecision && hasNome && hasUnidade && hasEmail;\nconst cadastroKeywords = ['cadastro','cadastrar','registrar','acesso','quero me cadastrar','como faço cadastro','preciso me registrar'];\nconst isCadastroRequest = !shouldIgnore && !isSindicoDecision && !isCompleteData && cadastroKeywords.some(k=>lowerText.includes(k));\nconst isGenericMessage = !shouldIgnore && !isSindicoDecision && !isCompleteData && !isCadastroRequest;\n\nreturn [{ senderPhone, senderName, messageText, shouldIgnore, isSindicoDecision, isCompleteData, isCadastroRequest, isGenericMessage, hasNome, hasUnidade, hasEmail, sindicoNumber }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1744, -288], "id": "4a6ea78b-8d1d-431a-9fad-05468f4931a9", "name": "Parse Mensagem WhatsApp"}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "={{ $json.senderPhone }}@c.us", "text": "O<PERSON><PERSON> {{ $json.senderName }}! 👋\\n\\nPara solicitar acesso ao assistente do condomínio, preciso das seguintes informações:\\n\\n📋 **Envie uma mensagem com:**\\n\\n• Nome: Seu nome completo\\n• Unidade: Apartamento/casa\\n• E-mail: <EMAIL>\\n\\n**Exemplo:**\\nNome: <PERSON>\\nUnidade: Apto 101\\nE-mail: <EMAIL>\\n\\n✅ Após enviar, o síndico receberá sua solicitação para aprovação!", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-1216, -496], "id": "4316b4d9-5551-4621-b9d5-61ba068ada70", "name": "WAHA - Enviar Instruções", "credentials": {"wahaApi": {"id": "yAwHJnP51sO5JHxp", "name": "WAHA account"}}}, {"parameters": {"jsCode": "// Parse dados completos da mensagem\nconst text = $json.messageText;\nconst senderPhone = $json.senderPhone;\nconst senderName = $json.senderName;\n\nfunction pick(lineLabel, text) {\n  const rx = new RegExp(lineLabel + '\\\\s*[:|-]\\\\s*(.+)', 'i');\n  const m = text.match(rx);\n  return m ? m[1].trim() : '';\n}\n\nconst parsed_name = pick('nome', text) || senderName;\nconst parsed_unidade = pick('unidade', text) || pick('apto', text) || '';\nconst emailRx = /[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}/i;\nconst emailMatch = text.match(emailRx);\nconst parsed_email = emailMatch ? emailMatch[0] : '';\n\nreturn {\n  raw_from: senderPhone + '@c.us',\n  raw_subject: 'Solicitação WhatsApp',\n  raw_body: text,\n  parsed_name,\n  parsed_email,\n  parsed_phone: senderPhone,\n  parsed_unidade,\n  sender_name: senderName\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1216, -288], "id": "f5699c5c-da35-49d7-84c2-62373a92e47d", "name": "<PERSON><PERSON>"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO allowed_phones (phone, nome, email, unidade, status)\\nVALUES ('{{ $json.parsed_phone }}', '{{ $json.parsed_name }}', '{{ $json.parsed_email }}', '{{ $json.parsed_unidade }}', 'pending')\\nON CONFLICT (phone)\\nDO UPDATE SET\\n  nome = COALESCE(EXCLUDED.nome, allowed_phones.nome),\\n  email = COALESCE(EXCLUDED.email, allowed_phones.email),\\n  unidade = COALESCE(EXCLUDED.unidade, allowed_phones.unidade)\\nWHERE allowed_phones.status <> 'blocked';", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-976, -368], "id": "f729e04a-b435-4faa-b1c5-a62e07de9272", "name": "Upsert Allowlist (pending)", "credentials": {"postgres": {"id": "lsjZDarHvhH0e7fR", "name": "SINDICO"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO onboarding_requests (source, raw_from, raw_subject, raw_body, parsed_name, parsed_email, parsed_phone, parsed_unidade)\\nVALUES ('whatsapp', '{{ $json.raw_from }}', '{{ $json.raw_subject }}', '{{ $json.raw_body }}', '{{ $json.parsed_name }}', '{{ $json.parsed_email }}', '{{ $json.parsed_phone }}', '{{ $json.parsed_unidade }}');", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-976, -160], "id": "5de8968f-2ea0-4774-9766-8ffc243a00ae", "name": "<PERSON><PERSON>", "credentials": {"postgres": {"id": "lsjZDarHvhH0e7fR", "name": "SINDICO"}}}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "={{ $json.parsed_phone }}@c.us", "text": "✅ **Solicitação Recebida!**\\n\\nObrigado {{ $json.parsed_name }}!\\n\\n📋 **Dados recebidos:**\\n• Nome: {{ $json.parsed_name }}\\n• Unidade: {{ $json.parsed_unidade }}\\n• E-mail: {{ $json.parsed_email }}\\n• Telefone: {{ $json.parsed_phone }}\\n\\n⏳ Sua solicitação foi enviada ao síndico para aprovação.\\nVocê será notificado em breve!", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-736, -288], "id": "bb0302e6-a579-4689-ac41-d65811fb564c", "name": "WAHA - <PERSON><PERSON><PERSON><PERSON>", "credentials": {"wahaApi": {"id": "yAwHJnP51sO5JHxp", "name": "WAHA account"}}}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "<EMAIL>", "text": "🏢 **NOVA SOLICITAÇÃO DE CADASTRO**\\n\\n👤 **Nome:** {{ $json.parsed_name }}\\n🏠 **Unidade:** {{ $json.parsed_unidade }}\\n📱 **Telefone:** {{ $json.parsed_phone }}\\n📧 **E-mail:** {{ $json.parsed_email }}\\n\\n---\\n\\n✅ Para **APROVAR**, responda:\\n`APROVAR {{ $json.parsed_phone }}`\\n\\n❌ Para **REJEITAR**, responda:\\n`REJEITAR {{ $json.parsed_phone }}`\\n\\n⚠️ **Importante:** Use exatamente o formato acima.", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-480, -288], "id": "8dc1e94d-7d50-447a-ba0f-f5bb46c12e6e", "name": "WAHA - Solicitar Aprovação", "credentials": {"wahaApi": {"id": "yAwHJnP51sO5JHxp", "name": "WAHA account"}}}, {"parameters": {"jsCode": "// Parse decisão APROVAR/REJEITAR do síndico\\nconst text = $json.messageText;\\nconst senderPhone = $json.senderPhone;\\n\\nconst firstLine = text.split(/\\\\r?\\\\n/)[0].trim();\\nlet decision = '';\\nlet phone = '';\\n\\nconst m = firstLine.match(/^(APROVAR|REJEITAR)\\\\s+([0-9+()\\\\-\\\\s]+)/i);\\nif (m) {\\n  decision = m[1].toUpperCase();\\n  phone = m[2];\\n}\\n\\nfunction normPhone(input) {\\n  let d = String(input || '').replace(/[^0-9]+/g, '');\\n  if (d.startsWith('55') && d.length > 11) {\\n    d = d.slice(d.length - 11);\\n  }\\n  if (d.length === 10 || d.length === 11) {\\n    if (!d.startsWith('55')) d = '55' + d;\\n  }\\n  return d;\\n}\\n\\nconst parsed_phone = normPhone(phone);\\nconst onboarding_status = decision === 'APROVAR' ? 'approved' : decision === 'REJEITAR' ? 'rejected' : 'invalid';\\nconst allowed_status = onboarding_status === 'approved' ? 'approved' : onboarding_status === 'rejected' ? 'blocked' : null;\\n\\nif (!parsed_phone || !allowed_status) {\\n  return { error: 'Comando inválido. Use APROVAR <telefone> ou REJEITAR <telefone>.' };\\n}\\n\\nreturn {\\n  parsed_phone,\\n  onboarding_status,\\n  allowed_status,\\n  sindico_number: senderPhone\\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-1216, -64], "id": "a43ba676-000a-4f0d-9728-cc8a2823db62", "name": "<PERSON><PERSON> Síndico"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO allowed_phones (phone, status)\\nVALUES ('{{ $json.parsed_phone }}', '{{ $json.allowed_status }}')\\nON CONFLICT (phone)\\nDO UPDATE SET\\n  status = EXCLUDED.status,\\n  updated_at = NOW();", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-976, 32], "id": "9b52fee2-87d6-4e84-8f4e-89f63e3e0b81", "name": "<PERSON><PERSON><PERSON><PERSON> (Allowlist)", "credentials": {"postgres": {"id": "lsjZDarHvhH0e7fR", "name": "SINDICO"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH sel AS (\\n  SELECT id FROM onboarding_requests\\n  WHERE parsed_phone = '{{ $json.parsed_phone }}'\\n  ORDER BY id DESC\\n  LIMIT 1\\n)\\nUPDATE onboarding_requests\\nSET status = '{{ $json.onboarding_status }}', processed_at = NOW()\\nWHERE id IN (SELECT id FROM sel);", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-736, 16], "id": "ac669547-6210-4568-8cc4-37c29212bfe7", "name": "Marcar Solicitação Processada", "credentials": {"postgres": {"id": "lsjZDarHvhH0e7fR", "name": "SINDICO"}}}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "={{ $json.parsed_phone }}@c.us", "text": "={{ $json.onboarding_status === 'approved' ? '✅ **Cadastro Aprovado!**\\n\\nParabéns! Seu cadastro foi aprovado pelo síndico.\\nVocê já pode falar com o assistente do condomínio.' : '❌ **Cadastro Não Aprovado**\\n\\nSeu cadastro não foi aprovado no momento.\\nVerifique seus dados e tente novamente ou entre em contato com a administração.' }}", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-480, -64], "id": "2668d7e5-3a13-4f8e-8bac-b35bc19df750", "name": "WAHA - Notificar <PERSON>", "credentials": {"wahaApi": {"id": "yAwHJnP51sO5JHxp", "name": "WAHA account"}}}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "<EMAIL>", "text": "=('✅ **Decisão Processada**\\n\\n' + ($json.onboarding_status === 'approved' ? 'Cadastro APROVADO para ' + $json.parsed_phone : 'Cadastro REJEITADO para ' + $json.parsed_phone) + '\\n\\nO morador foi notificado automaticamente.')", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-240, 16], "id": "52196e4f-6791-4b78-9593-7d3897b43025", "name": "WAHA - Confirmar ao Síndico", "credentials": {"wahaApi": {"id": "yAwHJnP51sO5JHxp", "name": "WAHA account"}}}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "={{ $('Webhook - Mensagens WhatsApp').item.json.body.payload._data.from }}", "text": "Olá! 👋 🤖 Sou o assistente de cadastro do condomínio! 📋 Para se cadastrar e acessar o sistema, por favor, envie as seguintes informações:  Nome: Seu nome completo Unidade: Seu apartamento ou casa E-mail: Um e-mail válido  💡 Exemplo de como enviar:  Nome: Ana Oliveira  Unidade: Apto 202  E-mail: ana.olive<PERSON>@email.com ✅ Após enviar, o síndico revisará sua solicitação", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-1216, 144], "id": "f7c41820-309f-46e0-8ae7-a324916728d7", "name": "WAHA - <PERSON><PERSON><PERSON><PERSON>", "credentials": {"wahaApi": {"id": "yAwHJnP51sO5JHxp", "name": "WAHA account"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT status FROM allowed_phones WHERE phone = '{{$json.senderPhone}}' LIMIT 1;", "options": {"continueIfEmpty": true}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-1216, 352], "id": "c6d2c8eb-2b33-4a2d-8d3a-0b70e739d8a2", "name": "Check Allowlist", "credentials": {"postgres": {"id": "lsjZDarHvhH0e7fR", "name": "SINDICO"}}}, {"parameters": {"conditions": {"boolean": [{"value1": "={{ $items().length > 0 && $json.status !== 'blocked' }}"}]}}, "id": "a33b7a2b-7e8f-4a6a-8c09-6fdb4cf4c0e0", "name": "IF Autorizado?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-992, 352]}, {"parameters": {"workflowType": "chain", "options": {}}, "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "typeVersion": 1.5, "position": [-768, 256], "id": "7e8eab3e-8b67-4a04-b0fe-0d3e7a9b47e2", "name": "Redis <PERSON>", "credentials": {"redis": {"id": "redis-creds-id", "name": "Redis"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-544, 256], "id": "3c5cefe1-3a07-4f73-9a4c-a5af4f3b2fa4", "name": "DeepSeek Chat Model", "credentials": {"deepseekApi": {"id": "deepseek-creds-id", "name": "DeepSeek"}}}, {"parameters": {"jsCode": "// Extrai reply_text da saída do modelo\nlet output = $json.output ?? $json.text ?? $json.message ?? '';\nlet reply = '';\nfunction tryParseAny(s){ try { return typeof s === 'string' ? JSON.parse(s) : s; } catch { return null; } }\nlet parsed = tryParseAny(output);\nif (!parsed && typeof output === 'string'){ const m = output.match(/```(?:json)?\\s*([\\s\\S]*?)```/); if (m) parsed = tryParseAny(m[1]); }\nif (!parsed && typeof output === 'string'){ const m2 = output.match(/\\{[\\s\\S]*\\}/); if (m2) parsed = tryParseAny(m2[0]); }\nif (parsed && typeof parsed === 'object'){ reply = parsed.resposta_usuario || parsed.reply || ''; }\nif (!reply){ reply = typeof output === 'string' && output.trim() ? output.trim() : 'Como posso ajudar você?'; }\nreturn { ...$json, reply_text: reply, normPhone: $json.senderPhone };"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-320, 256], "id": "f8f1f2e5-7a1c-4c8b-8e06-2dcd8a2f6ab9", "name": "Parse Resposta IA"}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "={{ $json.senderPhone }}@c.us", "text": "={{$json.reply_text || 'Mensagem processada.'}}", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-96, 256], "id": "9a2d0b6c-1c3b-4c86-9b88-7a7b5c3f47a8", "name": "WAHA - Responder IA", "credentials": {"wahaApi": {"id": "yAwHJnP51sO5JHxp", "name": "WAHA account"}}}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.shouldIgnore }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}, "id": "should-ignore"}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a94b150d-9383-4ce9-aa30-41b8c7c60a5f", "leftValue": "={{ $json.isSindicoDecision }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a6bb5602-385b-4620-a097-60fa0eb603b3", "leftValue": "={{ $json.isCompleteData }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "66851cd7-e65b-4189-8324-3a476121cb3a", "leftValue": "={{ $json.isCadastroRequest }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a934afa8-da9c-44d7-85cd-c67531491f10", "leftValue": "={{ $json.isGenericMessage }}", "rightValue": "true", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [-1520, -288], "id": "660ca1a6-4716-4751-b91d-c68bcf1b8491", "name": "Switch"}], "pinData": {"Webhook - Mensagens WhatsApp": [{"json": {"headers": {"host": "auto.uniqsolutions.com.br", "x-forwarded-scheme": "https", "x-forwarded-proto": "https", "x-forwarded-for": "*************, **************", "x-real-ip": "**************", "content-length": "2815", "content-type": "application/json", "user-agent": "WAHA/2025.8.1", "accept-encoding": "gzip, br", "x-webhook-timestamp": "1755735488734", "cf-ray": "9725f394fcb847af-DFW", "x-webhook-request-id": "01K34ZTY701MGCHVWHTEKPDXJX", "accept": "application/json, text/plain, */*", "cdn-loop": "cloudflare; loops=1", "cf-connecting-ip": "*************", "cf-ipcountry": "US", "cf-visitor": "{\"scheme\":\"https\"}"}, "params": {}, "query": {}, "body": {"id": "evt_01k34zty6xqayr0d6jj2t0er8m", "timestamp": 1755735488734, "event": "message", "session": "default", "metadata": {}, "me": {"id": "<EMAIL>", "pushName": "Unia"}, "payload": {"id": "false_5522981356255@c.us_3EB09602239C0C0FD9C4C5", "timestamp": 1755735487, "from": "<EMAIL>", "fromMe": false, "source": "app", "to": "<EMAIL>", "body": "ola", "hasMedia": false, "media": null, "ack": 1, "ackName": "SERVER", "vCards": [], "_data": {"id": {"fromMe": false, "remote": "<EMAIL>", "id": "3EB09602239C0C0FD9C4C5", "_serialized": "false_5522981356255@c.us_3EB09602239C0C0FD9C4C5"}, "viewed": false, "body": "ola", "type": "chat", "t": 1755735487, "notifyName": "Uniq", "from": "<EMAIL>", "to": "<EMAIL>", "ack": 1, "invis": false, "isNewMsg": true, "star": false, "kicNotified": false, "recvFresh": true, "isFromTemplate": false, "pollInvalidated": false, "isSentCagPollCreation": false, "latestEditMsgKey": null, "latestEditSenderTimestampMs": null, "mentionedJidList": [], "groupMentions": [], "isEventCanceled": false, "eventInvalidated": false, "isVcardOverMmsDocument": false, "isForwarded": false, "isQuestion": false, "labels": [], "hasReaction": false, "viewMode": "VISIBLE", "messageSecret": {"0": 200, "1": 14, "2": 4, "3": 230, "4": 222, "5": 42, "6": 104, "7": 61, "8": 41, "9": 204, "10": 206, "11": 44, "12": 122, "13": 176, "14": 29, "15": 55, "16": 243, "17": 192, "18": 144, "19": 3, "20": 29, "21": 82, "22": 221, "23": 84, "24": 26, "25": 103, "26": 245, "27": 124, "28": 153, "29": 61, "30": 135, "31": 179}, "productHeaderImageRejected": false, "lastPlaybackProgress": 0, "isDynamicReplyButtonsMsg": false, "isCarouselCard": false, "parentMsgId": null, "callSilenceReason": null, "isVideoCall": false, "callDuration": null, "callCreator": null, "callParticipants": null, "isCallLink": null, "callLinkToken": null, "isMdHistoryMsg": false, "stickerSentTs": 0, "isAvatar": false, "lastUpdateFromServerTs": 0, "invokedBotWid": null, "bizBotType": null, "botResponseTargetId": null, "botPluginType": null, "botPluginReferenceIndex": null, "botPluginSearchProvider": null, "botPluginSearchUrl": null, "botPluginSearchQuery": null, "botPluginMaybeParent": false, "botReelPluginThumbnailCdnUrl": null, "botMessageDisclaimerText": null, "botMsgBodyType": null, "reportingTokenInfo": {"reportingToken": {"0": 94, "1": 196, "2": 227, "3": 98, "4": 38, "5": 224, "6": 171, "7": 62, "8": 141, "9": 137, "10": 251, "11": 217, "12": 203, "13": 5, "14": 122, "15": 184}, "version": 2, "reportingTag": {"0": 1, "1": 10, "2": 7, "3": 32, "4": 181, "5": 216, "6": 162, "7": 76, "8": 8, "9": 34, "10": 45, "11": 115, "12": 179, "13": 122, "14": 111, "15": 192, "16": 167, "17": 226, "18": 250, "19": 47}}, "requiresDirectConnection": null, "bizContentPlaceholderType": null, "hostedBizEncStateMismatch": false, "senderOrRecipientAccountTypeHosted": false, "placeholderCreatedWhenAccountIsHosted": false, "galaxyFlowDisabled": false, "links": []}}, "engine": "WEBJS", "environment": {"version": "2025.8.1", "engine": "WEBJS", "tier": "CORE", "browser": "/usr/bin/chromium"}}, "webhookUrl": "https://auto.uniqsolutions.com.br/webhook/sindico-decisao", "executionMode": "production"}}]}, "connections": {"Webhook - Mensagens WhatsApp": {"main": [[{"node": "Parse Mensagem WhatsApp", "type": "main", "index": 0}]]}, "Parse Mensagem WhatsApp": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "Parse Dados Completos": {"main": [[{"node": "Upsert Allowlist (pending)", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Upsert Allowlist (pending)": {"main": [[{"node": "WAHA - <PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Salvar Solicitação": {"main": [[{"node": "WAHA - <PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}, "Check Allowlist": {"main": [[{"node": "IF Autorizado?", "type": "main", "index": 0}]]}, "IF Autorizado?": {"main": [[{"node": "WAHA - <PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "Redis <PERSON>", "type": "main", "index": 0}]]}, "Redis Chat Memory": {"main": [[{"node": "DeepSeek Chat Model", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"main": [[{"node": "Parse Resposta IA", "type": "main", "index": 0}]]}, "Parse Resposta IA": {"main": [[{"node": "WAHA - Responder IA", "type": "main", "index": 0}]]}, "WAHA - Confirmar Recebimento": {"main": [[{"node": "WAHA - Solicitar Aprovação", "type": "main", "index": 0}]]}, "Parse Decisão Síndico": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON> (Allowlist)", "type": "main", "index": 0}]]}, "Aplicar Decisão (Allowlist)": {"main": [[{"node": "Marcar Solicitação Processada", "type": "main", "index": 0}]]}, "Marcar Solicitação Processada": {"main": [[{"node": "WAHA - Notificar <PERSON>", "type": "main", "index": 0}, {"node": "WAHA - Confirmar ao Síndico", "type": "main", "index": 0}]]}, "Switch": {"main": [[], [{"node": "<PERSON><PERSON> Síndico", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}], [{"node": "WAHA - Enviar Instruções", "type": "main", "index": 0}], [{"node": "Check Allowlist", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "aac02ef7-1fbd-4c52-b832-1a081575df80", "meta": {"templateCredsSetupCompleted": true, "instanceId": "26bb5e00e3b41650f1c80a17ba0b2bc2c2393bb92f9a42c3ffc7c225c9d35205"}, "id": "oa7zwTaqKD1s2TeN", "tags": []}