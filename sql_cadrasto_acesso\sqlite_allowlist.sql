-- SQLite DDL para onboarding/allowlist

-- Tabela principal de autorização
CREATE TABLE IF NOT EXISTS allowed_phones (
  phone TEXT PRIMARY KEY,
  nome TEXT,
  email TEXT,
  unidade TEXT,
  status TEXT NOT NULL DEFAULT 'approved',
  created_at TEXT DEFAULT (datetime('now')),
  updated_at TEXT DEFAULT (datetime('now'))
);

-- <PERSON><PERSON> para manter updated_at
CREATE TRIGGER IF NOT EXISTS trg_allowed_phones_updated
AFTER UPDATE ON allowed_phones
FOR EACH ROW
BEGIN
  UPDATE allowed_phones SET updated_at = datetime('now') WHERE phone = NEW.phone;
END;

-- Log de onboarding por e-mail (opcional)
CREATE TABLE IF NOT EXISTS onboarding_requests (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  source TEXT NOT NULL DEFAULT 'email',
  raw_from TEXT,
  raw_subject TEXT,
  raw_body TEXT,
  parsed_name TEXT,
  parsed_email TEXT,
  parsed_phone TEXT,
  parsed_unidade TEXT,
  status TEXT NOT NULL DEFAULT 'received',
  error_msg TEXT,
  created_at TEXT DEFAULT (datetime('now')),
  processed_at TEXT
);

-- Índices úteis
CREATE INDEX IF NOT EXISTS idx_allowed_phones_status ON allowed_phones(status);
CREATE INDEX IF NOT EXISTS idx_allowed_phones_email ON allowed_phones(email);
