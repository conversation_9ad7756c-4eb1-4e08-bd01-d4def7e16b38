# Onboarding por E-mail (Cadastro/Allowlist via caixa postal)

Este guia descreve o padrão de e-mail que os moradores devem enviar para solicitar cadastro e como o síndico pode aprovar/reprovar diretamente pelo e-mail, sem entrar no Supabase. O fluxo usa as tabelas `allowed_phones` e `onboarding_requests` (ver `sql_cadrasto_acesso/postgres_allowlist.sql`).

## Visão geral do fluxo
1. Morador envia um e-mail de solicitação de cadastro para a caixa postal do condomínio.
2. n8n lê a caixa postal (IMAP), registra a solicitação em `onboarding_requests` e marca/insere o telefone em `allowed_phones` como `pending` (se ainda não existir).
3. n8n envia um e-mail ao síndico pedindo aprovação com 2 opções:
   - Responder o e-mail com APROVAR/REJEITAR (modo “reply”).
   - Clicar em links de Aprovar/Rejeitar (modo “link”).
4. A decisão atualiza o `status` em `allowed_phones` e notifica o morador por WhatsApp (via WAHA) e/ou e-mail.

## Padrão de e-mail do morador (entrada)
- Para: <EMAIL> (exemplo)
- Assunto (recomendado): "Cadastro de acesso - Unidade 34B - João da Silva"
- Corpo (recomendado):
  - Nome: João da Silva
  - Telefone: (11) 9 9123-4567
  - E-mail: <EMAIL>
  - Unidade: 34B

O parser também aceita textos livres; o telefone é extraído por regex e normalizado. Quanto mais estruturado, melhor.

### Regras de parsing (sugestão)
- Telefone: detectar formatos BR com/sem DDI/DDD e normalizar para `5511XXXXXXXXX` (11 dígitos). Exemplo de regex:
  - `/((?:\+?55)?\s*\(?\d{2}\)?\s*9?\s*\d{4}[\s-]?\d{4})/gi`
- Nome: procurar após "Nome:" ou primeira linha do corpo quando claro.
- Unidade: procurar após "Unidade:" ou padrões como "Apto 34B".
- E-mail: capturar por regex padrão de e-mail se fornecido.

## Aprovação pelo síndico na própria caixa postal
Você tem duas formas sem acessar o Supabase:

1) Aprovação por resposta (reply)
- O n8n envia ao síndico um e-mail com um código da solicitação, ex.: `REQ-1024`.
- O síndico responde ao e-mail com uma linha no início do corpo:
  - `APROVAR REQ-1024`
  - ou `REJEITAR REQ-1024`
- Alternativa por telefone: `APROVAR 5511999999999` (o fluxo valida se o telefone bate com a requisição).
- O n8n monitora a mesma caixa postal (IMAP) para respostas, detecta o comando e aplica a decisão.

2) Aprovação por links (botões)
- O e-mail ao síndico contém dois links:
  - Aprovar: `https://seu-n8n/webhook/approve?id=REQ-1024&t=<token>`
  - Rejeitar: `https://seu-n8n/webhook/reject?id=REQ-1024&t=<token>`
- O token é assinado (JWT ou HMAC) e de uso único. Ao clicar, o n8n atualiza o status e retorna uma página de confirmação.

## Esquema de dados (resumo)
- `allowed_phones(phone, nome, email, unidade, status, created_at, updated_at)`
  - status: `approved | pending | blocked` (ou conforme seu check)
- `onboarding_requests(id, source, raw_from, raw_subject, raw_body, parsed_name, parsed_email, parsed_phone, parsed_unidade, status, error_msg, created_at, processed_at)`

## Blueprint de workflow n8n
- IMAP Email Trigger (Caixa de entrada "Onboarding")
  - Filtrar por assunto contendo "Cadastro".
- Code (Parse Onboarding Email)
  - Extrair nome/email/telefone/unidade e normalizar telefone.
- Postgres (Insert onboarding_requests)
  - Armazenar raw e parsed_*.
- Postgres (Upsert allowed_phones)
  - Se não existir, inserir com `status='pending'`; se existir e status != blocked, manter.
- SMTP Send (Solicitar Aprovação ao Síndico)
  - Corpo com resumo + instruções de reply e/ou links com tokens.
- IMAP Email Trigger (Aprovações)
  - Filtrar respostas do e-mail de aprovação.
- Code (Parse Decisão Síndico)
  - Ler `APROVAR REQ-xxxx` ou `REJEITAR ...` (ou telefone).
- Postgres (Apply Decision)
  - Atualizar `allowed_phones.status` para `approved`/`blocked`.
  - Atualizar `onboarding_requests.status` para `approved`/`rejected` e `processed_at`.
- WAHA (Send a text message)
  - Se aprovado e telefone válido, enviar mensagem de boas-vindas com orientações.
- SMTP Send (Confirmação ao Morador)
  - Opcional: e-mail confirmando a decisão.

## Configuração necessária
- IMAP (entrada): host/porta/SSL, pasta (ex.: "Onboarding"), intervalo de pooling.
- SMTP (envio): host/porta/SSL, remetente (ex.: sindicatura@... ).
- Postgres: credenciais e acesso às tabelas acima.
- WAHA: base URL e token; instância conectada.
- Webhooks de aprovação: criar 2 webhooks no n8n (`/approve` e `/reject`) com verificação de token.

## Exemplos prontos
- Assunto: `Cadastro de acesso - Unidade 12C - Maria Oliveira`
- Corpo:
  - `Nome: Maria Oliveira`
  - `Telefone: (11) 9 8765-4321`
  - `E-mail: <EMAIL>`
  - `Unidade: 12C`

- E-mail ao síndico (resumo):
  - `Solicitação REQ-1042: Maria Oliveira (12C) - 5511987654321`
  - `Responda com: APROVAR REQ-1042` ou `REJEITAR REQ-1042`
  - ou use: `[Aprovar]` / `[Rejeitar]`

## Segurança e boas práticas
- Restrinja a aprovação por e-mail: aceite comandos apenas do e-mail do síndico (whitelist) e/o valide tokens de link.
- Tokens de link com expiração e uso único; associe ao id da solicitação.
- Rate limit e logs em `onboarding_requests` para auditoria.
- Evite aceitar aprovações por telefone/e-mail que não correspondam ao parsed da solicitação.

## Integração com o fluxo WhatsApp
- Assim que aprovado, o número passa a `approved` em `allowed_phones` e o fluxo WhatsApp (Webhook Entrada1) seguirá pela rota da IA normalmente.
- Se rejeitado, mantenha `blocked` e opcionalmente envie orientações de correção.
