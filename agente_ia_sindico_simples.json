{"name": "Agente IA Síndico - WhatsApp Evolution", "nodes": [{"parameters": {"path": "sindico-webhook", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 300], "id": "webhook-entrada", "name": "Webhook Entrada", "webhookId": "sindico-webhook"}, {"parameters": {"functionCode": "// Extrair o número do telefone do webhook\nlet from = $json.body?.remoteJid || $json.body?.from || $json.remoteJid || $json.from || '';\n\n// Remover prefixos e caracteres não numéricos\nfrom = String(from).replace(/^whatsapp:|@s.whatsapp.net$/g, '').replace(/[^0-9+]/g, '');\nlet digits = from.replace(/[^0-9]+/g, '');\n\n// Normalizar para formato brasileiro (11 dígitos)\nif (digits.startsWith('55') && digits.length > 11) {\n  digits = digits.slice(digits.length - 11);\n}\n\n// Extrair mensagem do webhook\nconst message = $json.body?.body || $json.body?.message?.text || $json.body?.text || $json.message || '';\n\nreturn { \n  ...$json, \n  normPhone: digits,\n  messageText: message,\n  originalFrom: from\n};"}, "id": "normalizar-telefone", "name": "Normalizar Telefone", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT phone, nome, unidade, status FROM allowed_phones WHERE phone='{{$json.normPhone}}' AND status='approved' LIMIT 1;", "options": {}}, "id": "check-allowlist", "name": "Check Allowlist", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [600, 300], "credentials": {"postgres": {"id": "postgres-credential", "name": "PostgreSQL"}}}, {"parameters": {"functionCode": "// Verificar se o telefone está autorizado\nconst hasData = $json.phone ? true : false;\n\nif (hasData) {\n  return {\n    ...$json,\n    allowed: true,\n    nome: $json.nome,\n    unidade: $json.unidade\n  };\n} else {\n  return {\n    ...$json,\n    allowed: false,\n    nome: null,\n    unidade: null,\n    reply_text: 'Número não autorizado. Envie um e-mail para cadastro contendo seu telefone e unidade.'\n  };\n}"}, "id": "avaliar-auth", "name": "Avaliar Autorização", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [800, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.allowed}}", "value2": true}]}}, "id": "if-autorizado", "name": "IF Autorizado?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1000, 300]}, {"parameters": {"method": "POST", "url": "={{$env.EVOLUTION_API_URL}}/message/sendText/{{$env.EVOLUTION_INSTANCE}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "{{$env.EVOLUTION_API_KEY}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonParameters": true, "bodyParametersJson": "={\n  \"number\": \"{{$json.normPhone || $json.originalFrom}}\",\n  \"text\": \"{{$json.reply_text || 'Erro no processamento da mensagem.'}}\"\n}", "options": {}}, "id": "enviar-whatsapp", "name": "Enviar WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1200, 500]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "Olá {{$json.nome || 'usuário'}}! 👋\\n\\nSou o assistente virtual do condomínio. Como posso ajudar você hoje?\\n\\n📋 Posso ajudar com:\\n• Agendar eventos na área comum\\n• Emitir segunda via de boletos\\n• Abrir chamados de manutenção\\n• Enviar avisos de vencimento\\n\\nConte-me o que precisa! 😊"}]}, "options": {}}, "id": "resposta-inicial", "name": "Resposta Inicial", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [1200, 300]}], "connections": {"Webhook Entrada": {"main": [[{"node": "Normalizar Telefone", "type": "main", "index": 0}]]}, "Normalizar Telefone": {"main": [[{"node": "Check Allowlist", "type": "main", "index": 0}]]}, "Check Allowlist": {"main": [[{"node": "Avaliar Autorização", "type": "main", "index": 0}]]}, "Avaliar Autorização": {"main": [[{"node": "IF Autorizado?", "type": "main", "index": 0}]]}, "IF Autorizado?": {"main": [[{"node": "Resposta Inicial", "type": "main", "index": 0}], [{"node": "Enviar WhatsApp", "type": "main", "index": 0}]]}, "Resposta Inicial": {"main": [[{"node": "Enviar WhatsApp", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "tags": []}