{"name": "Agenteia_Sindico_Mcp", "nodes": [{"parameters": {"promptType": "define", "text": "={{$json.prompt_treinado}}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [16, 272], "id": "a8cf947c-2ec2-4f21-af11-b5851725c72e", "name": "AI Agent"}, {"parameters": {"jsCode": "// Extrai a mensagem utilizável da saída do Agente IA (DeepSeek) e prepara reply_text.\n// A saída pode ser um JSON (acao, params, resposta_usuario) ou texto livre.\nlet output = $json.output ?? $json.text ?? $json.message ?? '';\nlet reply = '';\nlet acao = 'conversar';\nlet params = {};\n\nfunction tryParseAny(s) {\n  try { return typeof s === 'string' ? JSON.parse(s) : s; } catch { return null; }\n}\n\n// 1) Tenta JSON direto\nlet parsed = tryParseAny(output);\n// 2) Se falhar, tenta trechos entre ```json ... ```\nif (!parsed && typeof output === 'string') {\n  const m = output.match(/```(?:json)?\\s*([\\n\\r\\t\\s\\S]*?)```/);\n  if (m) parsed = tryParseAny(m[1]);\n}\n// 3) Fallback: procura primeiro objeto { ... }\nif (!parsed && typeof output === 'string') {\n  const m2 = output.match(/\\{[\\s\\S]*\\}/);\n  if (m2) parsed = tryParseAny(m2[0]);\n}\n\nif (parsed && typeof parsed === 'object') {\n  acao = parsed.acao || 'conversar';\n  params = parsed.params || {};\n  reply = parsed.resposta_usuario || '';\n}\n\nif (!reply) {\n  // Se não conseguimos extrair resposta estruturada, usa o texto bruto\n  reply = typeof output === 'string' && output.trim() ? output.trim() : 'Como posso ajudar você?';\n}\n\nreturn { ...$json, acao, params, reply_text: reply };"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [208, 272], "id": "7f9a3b28-62b0-4244-8e6b-4f20c4b7a9b0", "name": "Parse Resposta IA"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-144, 512], "id": "9415e88e-d9b4-4ac4-8c23-41a9f58dcabe", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "EMVUGRG5w0bLD41Z", "name": "DeepSeek account"}}}, {"parameters": {"sessionKey": "={{$json.normPhone}}"}, "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "typeVersion": 1.5, "position": [80, 512], "id": "c41c2fa2-8f7b-46f2-984f-a431cfc5ddd0", "name": "Redis <PERSON>", "credentials": {"redis": {"id": "FwBRYM9IJHfqMtD0", "name": "Redis _padrao"}}}, {"parameters": {"path": "sindico-webhook", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-1168, 80], "id": "076c462c-2408-4f32-bce8-0b88880ad745", "name": "Webhook Entrada1", "webhookId": "sindico-webhook"}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.allowed}}", "value2": true}]}}, "id": "9797626c-a73e-40eb-a292-8ed31fda64a9", "name": "IF Autorizado?1", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-304, 240]}, {"parameters": {"jsCode": "// Extrair o número do telefone do webhook (pode vir em diferentes formatos)\nlet from = $json.body?.remoteJid || $json.body?.from || $json.remoteJid || $json.from || '';\n\n// Remover prefixos e caracteres não numéricos\nfrom = String(from).replace(/^whatsapp:|@s.whatsapp.net$/g, '').replace(/[^0-9+]/g, '');\nlet digits = from.replace(/[^0-9]+/g, '');\n\n// Normalizar para formato brasileiro (11 dígitos)\nif (digits.startsWith('55') && digits.length > 11) {\n  digits = digits.slice(digits.length - 11);\n}\n\n// Extrair mensagem do webhook\nconst message = $json.body?.body || $json.body?.message?.text || $json.body?.text || $json.message || '';\n\nreturn { \n  ...$json, \n  normPhone: digits,\n  messageText: message,\n  originalFrom: from\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-912, 112], "id": "44913d7e-d10b-43ed-86aa-c312f6e3e7a9", "name": "Normalizar Telefone"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT phone, nome, unidade, status FROM allowed_phones WHERE phone='{{$json.normPhone}}' AND status='approved' LIMIT 1;", "options": {"continueIfEmpty": true}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-768, 272], "id": "7c8b1201-f1eb-45ef-9c67-7d3ab6cb672b", "name": "Check Allowlist", "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"jsCode": "// Verificar se o telefone está autorizado\nconst hasData = $json.phone ? true : false;\n\nif (hasData) {\n  return {\n    ...$json,\n    allowed: true,\n    nome: $json.nome,\n    unidade: $json.unidade\n  };\n} else {\n  return {\n    ...$json,\n    allowed: false,\n    nome: null,\n    unidade: null,\n    reply_text: 'Número não autorizado. Envie um e-mail para cadastro contendo seu telefone e unidade.'\n  };\n}"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-576, 144], "id": "71e186d4-7a3b-496a-b37e-27c19c0eb667", "name": "Avaliar Autorização"}, {"parameters": {"jsCode": "// === TREINAMENTO E PREPARAÇÃO DE CONTEXTO PARA IA ===\n\n// 1. DADOS DO USUÁRIO\nconst userData = {\n  nome: $json.nome || 'Usuário',\n  unidade: $json.unidade || 'Não informada',\n  telefone: $json.normPhone || 'Não informado',\n  mensagem: $json.messageText || ''\n};\n\n// 2. BASE DE CONHECIMENTO - TREINAMENTO\nconst baseConhecimento = {\n  areas_comuns: [\n    'salão de festas', 'churrasqueira', 'piscina', 'quadra', \n    'playground', 'academia', 'espaço gourmet', 'salão de jogos'\n  ],\n  tipos_boleto: [\n    'condomínio', 'taxa extra', 'multa', 'água', 'gás', \n    'fundo de reserva', 'obra', 'segunda via'\n  ],\n  categorias_manutencao: [\n    'elétrica', 'hidráulica', 'pintura', 'jardinagem', \n    'limpeza', 'portaria', 'elevador', 'interfone'\n  ],\n  urgencias: [\n    'vazamento', 'falta de luz', 'elevador parado', \n    'portão quebrado', 'interfone', 'emergência'\n  ]\n};\n\n// 3. ANÁLISE INTELIGENTE DA MENSAGEM\nconst msgLower = userData.mensagem.toLowerCase();\n\n// Detectar intenções por palavras-chave\nconst intencoes = {\n  agendar: ['agendar', 'reservar', 'marcar', 'festa', 'evento', 'churrasqueira', 'salão', 'piscina'],\n  boleto: ['boleto', 'segunda via', 'pagamento', 'cobrança', 'taxa', 'valor', 'vencimento'],\n  manutencao: ['problema', 'quebrou', 'conserto', 'reparo', 'manutenção', 'defeito', 'chamado'],\n  aviso: ['aviso', 'comunicado', 'informar', 'notificar', 'vencimento', 'todos']\n};\n\nlet intencaoDetectada = 'conversar';\nlet confianca = 0;\n\nfor (const [acao, palavras] of Object.entries(intencoes)) {\n  const matches = palavras.filter(palavra => msgLower.includes(palavra)).length;\n  if (matches > confianca) {\n    confianca = matches;\n    intencaoDetectada = acao === 'agendar' ? 'agendar_evento' : \n                       acao === 'boleto' ? 'emitir_boleto' :\n                       acao === 'manutencao' ? 'solicitar_assistencia' :\n                       acao === 'aviso' ? 'enviar_aviso_vencimento' : 'conversar';\n  }\n}\n\n// 4. CONTEXTO ESTRUTURADO PARA IA\nconst contextoIA = {\n  usuario: userData,\n  conhecimento: baseConhecimento,\n  analise: {\n    intencao_detectada: intencaoDetectada,\n    confianca: confianca,\n    palavras_chave: msgLower.split(' ').filter(p => p.length > 2)\n  },\n  \n  exemplos: {\n    agendar_evento: {\n      acao: 'agendar_evento',\n      params: { titulo: 'Festa de aniversário', data: '2025-08-20', horario: '19:00', local: 'Salão de festas' },\n      resposta_usuario: '✅ Perfeito! Vou agendar o salão de festas para você. Que data e horário prefere?'\n    },\n    emitir_boleto: {\n      acao: 'emitir_boleto',\n      params: { competencia: '2025-08', tipo: 'condominio' },\n      resposta_usuario: '📄 Vou gerar a segunda via do seu boleto. Para qual competência (mês/ano)?'\n    },\n    solicitar_assistencia: {\n      acao: 'solicitar_assistencia',\n      params: { titulo: 'Problema elétrico', categoria: 'elétrica', descricao: 'Tomada sem energia' },\n      resposta_usuario: '🔧 Entendi o problema! Vou abrir um chamado de manutenção. Pode detalhar o que está acontecendo?'\n    }\n  }\n};\n\n// 5. PROMPT CONTEXTUALIZADO\nconst promptTreinado = `ASSISTENTE ESPECIALIZADO EM GESTÃO PREDIAL\n\nUSUÁRIO: ${userData.nome} - Unidade ${userData.unidade}\nMENSAGEM: \"${userData.mensagem}\"\n\nANÁLISE PRÉVIA:\n- Intenção detectada: ${intencaoDetectada}\n- Confiança: ${confianca} palavras-chave\n\nBASE DE CONHECIMENTO:\n- Áreas: ${baseConhecimento.areas_comuns.join(', ')}\n- Boletos: ${baseConhecimento.tipos_boleto.join(', ')}\n- Manutenção: ${baseConhecimento.categorias_manutencao.join(', ')}\n\nRESPONDA SEMPRE EM JSON:\n{\n  \"acao\": \"${intencaoDetectada}\",\n  \"params\": { \"detalhes\": \"específicos\" },\n  \"resposta_usuario\": \"mensagem amigável\"\n}\n\nSUA RESPOSTA:`;\n\nconsole.log('Contexto IA preparado:', JSON.stringify(contextoIA, null, 2));\n\nreturn {\n  ...$json,\n  contexto_ia: contextoIA,\n  prompt_treinado: promptTreinado,\n  intencao_detectada: intencaoDetectada,\n  confianca_analise: confianca\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, 0], "id": "52b980b9-c827-4c0d-93b5-648e29c89eb2", "name": "Preparar Contexto IA1"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1.1, "position": [304, 528], "id": "fdc55049-9f17-4c87-a848-70cdd5947617", "name": "MCP Client"}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "chatId": "={{$json.normPhone}}@c.us", "text": "={{$json.reply_text || $json.resposta_usuario || $json.output || 'Mensagem processada.'}}", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [544, 272], "id": "74859870-1593-4611-9c3e-a688947ec6e8", "name": "Send a text message"}], "pinData": {}, "connections": {"DeepSeek Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Redis Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Webhook Entrada1": {"main": [[{"node": "Normalizar Telefone", "type": "main", "index": 0}]]}, "IF Autorizado?1": {"main": [[{"node": "Preparar Contexto IA1", "type": "main", "index": 0}], [{"node": "Send a text message", "type": "main", "index": 0}]]}, "Normalizar Telefone": {"main": [[{"node": "Check Allowlist", "type": "main", "index": 0}]]}, "Check Allowlist": {"main": [[{"node": "Avaliar Autorização", "type": "main", "index": 0}]]}, "Avaliar Autorização": {"main": [[{"node": "IF Autorizado?1", "type": "main", "index": 0}]]}, "Preparar Contexto IA1": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "MCP Client": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Parse Resposta IA", "type": "main", "index": 0}]]}, "Parse Resposta IA": {"main": [[{"node": "Send a text message", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "fae1a2c6-cdc1-4b58-bd77-86cd21ab8430", "meta": {"instanceId": "26bb5e00e3b41650f1c80a17ba0b2bc2c2393bb92f9a42c3ffc7c225c9d35205"}, "id": "ga9cIRoN0ddettTB", "tags": []}