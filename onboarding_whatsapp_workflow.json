{"name": "Onboarding WhatsApp - <PERSON><PERSON><PERSON> Síndico", "nodes": [{"parameters": {"downloadAttachments": false, "options": {}}, "type": "n8n-nodes-base.emailReadImap", "typeVersion": 2, "position": [-1024, -64], "id": "a1b2c3d4-0001-1111-2222-333344445555", "name": "IMAP - Novas Solicitações"}, {"parameters": {"jsCode": "const raw_from = $json.from || ($json.headers && $json.headers.from) || '';\nconst raw_subject = $json.subject || ($json.headers && $json.headers.subject) || '';\nconst body = String($json.text || $json.textHtml || $json.html || '');\n\nfunction normPhone(input) {\n  if (!input) return '';\n  let digits = String(input).replace(/[^0-9]+/g, '');\n  if (digits.startsWith('55') && digits.length > 11) {\n    digits = digits.slice(digits.length - 11);\n  }\n  if (digits.length === 10 || digits.length === 11) {\n    if (!digits.startsWith('55')) digits = '55' + digits;\n  }\n  return digits;\n}\n\nconst phoneMatch = body.match(/((?:\\+?55)?\\s*\\(?\\d{2}\\)?\\s*9?\\s*\\d{4}[\\s-]?\\d{4})/i);\nconst parsed_phone = normPhone(phoneMatch ? phoneMatch[1] : '');\n\nfunction pick(lineLabel, text) {\n  const rx = new RegExp(lineLabel + '\\\\s*[:|-]\\\\s*(.+)', 'i');\n  const m = text.match(rx);\n  return m ? m[1].trim() : '';\n}\n\nconst parsed_name = pick('nome', body) || raw_from.replace(/<.*?>/g, '').split('\"').join('').split('(')[0].trim();\nconst parsed_unidade = pick('unidade', body) || pick('apto', body) || '';\nconst emailRx = /[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}/i;\nconst emailMatch = body.match(emailRx) || String(raw_from).match(emailRx);\nconst parsed_email = emailMatch ? emailMatch[0] : '';\n\nreturn {\n  raw_from,\n  raw_subject,\n  raw_body: body,\n  parsed_name,\n  parsed_email,\n  parsed_phone,\n  parsed_unidade\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-784, -64], "id": "a1b2c3d4-0002-1111-2222-333344445555", "name": "Parse Onboarding Email"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO allowed_phones (phone, nome, email, unidade, status)\nVALUES ('{{ $json.parsed_phone }}', '{{ $json.parsed_name }}', '{{ $json.parsed_email }}', '{{ $json.parsed_unidade }}', 'pending')\nON CONFLICT (phone)\nDO UPDATE SET\n  nome = COALESCE(EXCLUDED.nome, allowed_phones.nome),\n  email = COALESCE(EXCLUDED.email, allowed_phones.email),\n  unidade = COALESCE(EXCLUDED.unidade, allowed_phones.unidade)\nWHERE allowed_phones.status <> 'blocked';", "options": {"continueIfEmpty": true}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-544, -160], "id": "a1b2c3d4-0003-1111-2222-333344445555", "name": "Upsert Allowlist (pending)", "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO onboarding_requests (source, raw_from, raw_subject, raw_body, parsed_name, parsed_email, parsed_phone, parsed_unidade)\nVALUES ('email', '{{ $json.raw_from }}', '{{ $json.raw_subject }}', '{{ $json.raw_body }}', '{{ $json.parsed_name }}', '{{ $json.parsed_email }}', '{{ $json.parsed_phone }}', '{{ $json.parsed_unidade }}');", "options": {"continueIfEmpty": true}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-544, 16], "id": "a1b2c3d4-0004-1111-2222-333344445555", "name": "<PERSON><PERSON> Onboarding", "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "<EMAIL>", "text": "NOVA SOLICITAÇÃO DE CADASTRO\\n\\nNome: {{ $json.parsed_name }}\\nUnidade: {{ $json.parsed_unidade }}\\nTelefone: {{ $json.parsed_phone }}\\nE-mail: {{ $json.parsed_email }}\\n\\n---\\n\\nPara APROVAR, responda:\\nAPROVAR {{ $json.parsed_phone }}\\n\\nPara REJEITAR, responda:\\nREJEITAR {{ $json.parsed_phone }}\\n\\nImportante: Use exatamente o formato acima para que o sistema reconheça sua decisão.", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-320, 16], "id": "a1b2c3d4-0005-1111-2222-333344445555", "name": "WAHA - Solicitar Aprovação"}, {"parameters": {"httpMethod": "POST", "path": "sindico-decisao", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1024, 288], "id": "a1b2c3d4-0006-1111-2222-333344445555", "name": "Webhook - Decisões WAHA"}, {"parameters": {"jsCode": "// Parse decisão APROVAR/REJEITAR do WhatsApp\nconst body = $json.body || {};\nconst text = body.text || body.body || '';\nconst from = body.from || body.chatId || '';\n\n// Verifica se é do número do síndico\nconst sindicoNumber = '5522997986724';\nconst fromNumber = from.replace('@c.us', '').replace(/[^0-9]/g, '');\n\nif (fromNumber !== sindicoNumber) {\n  return { error: 'Mensagem não é do síndico autorizado' };\n}\n\nconst firstLine = text.split(/\\r?\\n/)[0].trim();\nlet decision = '';\nlet phone = '';\n\n// Parse APROVAR/REJEITAR\nconst m = firstLine.match(/^(APROVAR|REJEITAR)\\s+([0-9+()\\-\\s]+)/i);\nif (m) {\n  decision = m[1].toUpperCase();\n  phone = m[2];\n}\n\nfunction normPhone(input) {\n  let d = String(input || '').replace(/[^0-9]+/g, '');\n  if (d.startsWith('55') && d.length > 11) {\n    d = d.slice(d.length - 11);\n  }\n  if (d.length === 10 || d.length === 11) {\n    if (!d.startsWith('55')) d = '55' + d;\n  }\n  return d;\n}\n\nconst parsed_phone = normPhone(phone);\nconst onboarding_status = decision === 'APROVAR' ? 'approved' : decision === 'REJEITAR' ? 'rejected' : 'invalid';\nconst allowed_status = onboarding_status === 'approved' ? 'approved' : onboarding_status === 'rejected' ? 'blocked' : null;\n\nif (!parsed_phone || !allowed_status) {\n  return { error: 'Comando inválido. Use APROVAR <telefone> ou REJEITAR <telefone>.' };\n}\n\nreturn {\n  parsed_phone,\n  onboarding_status,\n  allowed_status,\n  sindico_number: fromNumber\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-784, 288], "id": "a1b2c3d4-0007-1111-2222-333344445555", "name": "<PERSON><PERSON> WhatsApp"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO allowed_phones (phone, status)\nVALUES ('{{ $json.parsed_phone }}', '{{ $json.allowed_status }}')\nON CONFLICT (phone)\nDO UPDATE SET\n  status = EXCLUDED.status,\n  updated_at = NOW();", "options": {"continueIfEmpty": true}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-544, 288], "id": "a1b2c3d4-0008-1111-2222-333344445555", "name": "<PERSON><PERSON><PERSON><PERSON> (Allowlist)", "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "WITH sel AS (\n  SELECT id FROM onboarding_requests\n  WHERE parsed_phone = '{{ $json.parsed_phone }}'\n  ORDER BY id DESC\n  LIMIT 1\n)\nUPDATE onboarding_requests\nSET status = '{{ $json.onboarding_status }}', processed_at = NOW()\nWHERE id IN (SELECT id FROM sel);", "options": {"continueIfEmpty": true}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-320, 288], "id": "a1b2c3d4-0009-1111-2222-333344445555", "name": "Marcar Solicitação Processada", "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "={{ $json.parsed_phone }}@c.us", "text": "={{ $json.onboarding_status === 'approved' ? 'Cadastro Aprovado! Olá! Seu cadastro foi aprovado pelo síndico. Você já pode falar com o assistente do condomínio.' : 'Cadastro Não Aprovado. Seu cadastro não foi aprovado no momento. Verifique seus dados e tente novamente ou entre em contato com a administração.' }}", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-96, 288], "id": "a1b2c3d4-0010-1111-2222-333344445555", "name": "WAHA - Notificar <PERSON>"}, {"parameters": {"resource": "Chatting", "operation": "Send Text", "session": "default", "chatId": "<EMAIL>", "text": "=('Decisão Processada\\n\\n' + ($json.onboarding_status === 'approved' ? 'Cadastro APROVADO para ' + $json.parsed_phone : 'Cadastro REJEITADO para ' + $json.parsed_phone) + '\\n\\nO morador foi notificado automaticamente.')", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [128, 288], "id": "a1b2c3d4-0011-1111-2222-333344445555", "name": "WAHA - Confirmar ao Síndico"}], "connections": {"IMAP - Novas Solicitações": {"main": [[{"node": "Parse Onboarding Email", "type": "main", "index": 0}]]}, "Parse Onboarding Email": {"main": [[{"node": "Upsert Allowlist (pending)", "type": "main", "index": 0}]]}, "Upsert Allowlist (pending)": {"main": [[{"node": "<PERSON><PERSON> Onboarding", "type": "main", "index": 0}]]}, "Salvar Onboarding": {"main": [[{"node": "WAHA - Solicitar Aprovação", "type": "main", "index": 0}]]}, "Webhook - Decisões WAHA": {"main": [[{"node": "<PERSON><PERSON> WhatsApp", "type": "main", "index": 0}]]}, "Parse Decisão WhatsApp": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON> (Allowlist)", "type": "main", "index": 0}]]}, "Aplicar Decisão (Allowlist)": {"main": [[{"node": "Marcar Solicitação Processada", "type": "main", "index": 0}]]}, "Marcar Solicitação Processada": {"main": [[{"node": "WAHA - Notificar <PERSON>", "type": "main", "index": 0}, {"node": "WAHA - Confirmar ao Síndico", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "pinData": {}}