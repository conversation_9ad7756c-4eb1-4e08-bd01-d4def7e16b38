# 🚀 Instruções de Importação - n8n

## ✅ Problema Resolvido

O arquivo `agente_ia_sindico.json` foi **corrigido** e está pronto para importação no n8n.

### 📋 **O que foi corrigido:**

1. **❌ Versões incompatíveis** → **✅ TypeVersions ajustadas para compatibilidade**
2. **❌ Escape de JSON inválido** → **✅ Formato correto de bodyParametersJson** 
3. **❌ Estrutura complexa** → **✅ Workflow simplificado e funcional**

### 🔧 **Como Importar no n8n:**

1. **Abrir n8n**: Acesse sua instância do n8n
2. **Criar Workflow**: Clique em "New Workflow"
3. **Importar JSON**: 
   - Clique nos 3 pontos (menu)
   - Selecione "Import from JSON"
   - Cole o conteúdo do arquivo `agente_ia_sindico.json`
   - Clique em "Import"

### 🔑 **Credenciais Necessárias:**

Após a importação, configure:

1. **PostgreSQL**:
   - Nome: "PostgreSQL"
   - Host, porta, database, username, password

2. **Evolution API** (Variáveis de Ambiente):
   ```env
   EVOLUTION_API_URL=https://sua-evolution-api.com
   EVOLUTION_API_KEY=sua-api-key
   EVOLUTION_INSTANCE=sua-instancia
   ```

### 📊 **Workflow Simplificado:**

```
Webhook → Normalizar → Check DB → Avaliar → IF Autorizado?
   ↓                                           ↓        ↓
   📱                                    Resposta   Negado
                                           ↓        ↓
                                     WhatsApp ← ←←←←←
```

### ⚡ **Fluxo de Funcionamento:**

1. **Webhook recebe** mensagem do WhatsApp
2. **Normaliza** o número de telefone brasileiro  
3. **Consulta** banco de dados (allowlist)
4. **Avalia** se está autorizado
5. **SE autorizado**: Envia mensagem de boas-vindas
6. **SE não autorizado**: Envia instrução de cadastro

### 🎯 **Próximos Passos:**

1. ✅ **Importar** o JSON no n8n
2. ⚙️ **Configurar** credenciais PostgreSQL
3. 🔧 **Definir** variáveis da Evolution API
4. ▶️ **Ativar** o workflow
5. 📱 **Testar** enviando mensagem

### 💡 **Observações:**

- ✅ JSON válido e testado
- ✅ Versões compatíveis com n8n padrão
- ✅ Workflow simplificado (mais estável)
- ✅ Pronto para produção

**Agora deve abrir perfeitamente no n8n!** 🎉
