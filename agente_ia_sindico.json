{"name": "My workflow 3", "nodes": [{"parameters": {"promptType": "define", "text": "={{$json.prompt_treinado}}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [-2880, -256], "id": "4f5a50c1-fa53-4377-833f-5de4d417a077", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-2928, -80], "id": "2af93c95-f3b5-4c2f-acf5-5ba2006894fc", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "EMVUGRG5w0bLD41Z", "name": "DeepSeek account"}}}, {"parameters": {"sessionKeyType": "fromInput", "sessionKey": "={{$json.normPhone}}"}, "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "typeVersion": 1.5, "position": [-2816, -64], "id": "00ecb474-064a-46f8-96d0-3c6ccbc9214c", "name": "Redis <PERSON>", "credentials": {"redis": {"id": "FwBRYM9IJHfqMtD0", "name": "Redis _padrao"}}}, {"parameters": {"dataType": "string", "value1": "={{$json.acao}}", "rules": {"rules": [{"value2": "agendar_evento"}, {"value2": "emitir_boleto", "output": 1}, {"value2": "solicitar_assistencia", "output": 2}, {"value2": "enviar_aviso_vencimento", "output": 3}]}, "fallbackOutput": 4}, "id": "861c4de1-892b-4caa-85fb-b2f7e706c49c", "name": "Switch Ações", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [-2240, -240]}, {"parameters": {"method": "POST", "url": "={{$env.CALENDAR_API_URL}}/eventos", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$env.CALENDAR_API_TOKEN}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "options": {}}, "id": "0266d94b-0839-4efe-961d-58c58c92e604", "name": "API Agendar Evento", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [-2032, -448]}, {"parameters": {"method": "POST", "url": "={{$env.PAYMENT_API_URL}}/boletos", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$env.PAYMENT_API_TOKEN}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "options": {}}, "id": "780b1d17-8f68-460d-b689-935cf41d61e1", "name": "API Emitir Boleto", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [-2032, -352]}, {"parameters": {"method": "POST", "url": "={{$env.TICKET_API_URL}}/chamados", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$env.TICKET_API_TOKEN}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "options": {}}, "id": "09de7fee-9aa2-4c2a-b531-fe375d318144", "name": "API Abrir Ticket", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [-2032, -240]}, {"parameters": {"method": "POST", "url": "={{$env.NOTIFICATION_API_URL}}/avisos", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$env.NOTIFICATION_API_TOKEN}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "options": {}}, "id": "654c5409-5966-4c15-a8bb-d6789936603d", "name": "API Preparar Avisos", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [-2032, -144]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "✅ Evento agendado: {{$json.params.titulo || 'Reserva'}}{{$json.data?.link ? ' - ' + $json.data.link : ''}}"}]}, "options": {}}, "id": "8b6b7a52-dc48-43b6-a02f-f4e4578b1c74", "name": "Set Resposta Agenda", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-1824, -448]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "🛠️ Chamado aberto com sucesso. Protocolo: {{$json.data?.id || $json.data?.protocolo || $json.data?.ticket || 'Não informado'}}"}]}, "options": {}}, "id": "d3814952-68c2-455f-ae35-86bfe4d48a76", "name": "Set Resposta Ticket", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-1824, -240]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "={{$json.resposta_usuario || 'Como posso ajudar você?'}}"}]}, "options": {}}, "id": "e38ee5db-ea27-49e2-b571-be12938bc9f9", "name": "Set Resposta Conversa", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-1824, -48]}, {"parameters": {"path": "sindico-webhook", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [-4112, -240], "id": "67761e1d-8dbc-4696-b3d1-6927525189dd", "name": "Webhook Entrada1", "webhookId": "sindico-webhook"}, {"parameters": {"functionCode": "// Extrair o número do telefone do webhook (pode vir em diferentes formatos)\nlet from = $json.body?.remoteJid || $json.body?.from || $json.remoteJid || $json.from || '';\n\n// Remover prefixos e caracteres não numéricos\nfrom = String(from).replace(/^whatsapp:|@s.whatsapp.net$/g, '').replace(/[^0-9+]/g, '');\nlet digits = from.replace(/[^0-9]+/g, '');\n\n// Normalizar para formato brasileiro (11 dígitos)\nif (digits.startsWith('55') && digits.length > 11) {\n  digits = digits.slice(digits.length - 11);\n}\n\n// Extrair mensagem do webhook\nconst message = $json.body?.body || $json.body?.message?.text || $json.body?.text || $json.message || '';\n\nreturn { \n  ...$json, \n  normPhone: digits,\n  messageText: message,\n  originalFrom: from\n};"}, "id": "08a1b8f8-fb51-4137-b0fe-cc85521a020c", "name": "Normalizar Telefone1", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [-3840, -240]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT phone, nome, unidade, status FROM allowed_phones WHERE phone='{{$json.normPhone}}' AND status='approved' LIMIT 1;", "additionalFields": {}}, "id": "c6d2bd26-0b0f-4151-8e02-b6981b5fb583", "name": "Check Allowlist1", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [-3616, -240]}, {"parameters": {"functionCode": "// Verificar se o telefone está autorizado\nconst hasData = $json.phone ? true : false;\n\nif (hasData) {\n  return {\n    ...$json,\n    allowed: true,\n    nome: $json.nome,\n    unidade: $json.unidade\n  };\n} else {\n  return {\n    ...$json,\n    allowed: false,\n    nome: null,\n    unidade: null,\n    reply_text: 'Número não autorizado. Envie um e-mail para cadastro contendo seu telefone e unidade.'\n  };\n}"}, "id": "262e3613-3cde-4c00-b9e1-a7cff9d7845c", "name": "Avaliar Autorização1", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [-3376, -240]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.allowed}}", "value2": true}]}}, "id": "9c2bae92-d5a2-4dcb-99be-d0e133cb6b6c", "name": "IF Autorizado?1", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [-3120, -240]}, {"parameters": {"functionCode": "// === TREINAMENTO E PREPARAÇÃO DE CONTEXTO PARA IA ===\n\n// 1. DADOS DO USUÁRIO\nconst userData = {\n  nome: $json.nome || 'Usuário',\n  unidade: $json.unidade || 'Não informada',\n  telefone: $json.normPhone || 'Não informado',\n  mensagem: $json.messageText || ''\n};\n\n// 2. BASE DE CONHECIMENTO - TREINAMENTO\nconst baseConhecimento = {\n  areas_comuns: [\n    'salão de festas', 'churrasqueira', 'piscina', 'quadra', \n    'playground', 'academia', 'espaço gourmet', 'salão de jogos'\n  ],\n  tipos_boleto: [\n    'condomínio', 'taxa extra', 'multa', 'água', 'gás', \n    'fundo de reserva', 'obra', 'segunda via'\n  ],\n  categorias_manutencao: [\n    'elétrica', 'hidráulica', 'pintura', 'jardinagem', \n    'limpeza', 'portaria', 'elevador', 'interfone'\n  ],\n  urgencias: [\n    'vazamento', 'falta de luz', 'elevador parado', \n    'portão quebrado', 'interfone', 'emergência'\n  ]\n};\n\n// 3. ANÁLISE INTELIGENTE DA MENSAGEM\nconst msgLower = userData.mensagem.toLowerCase();\n\n// Detectar intenções por palavras-chave\nconst intencoes = {\n  agendar: ['agendar', 'reservar', 'marcar', 'festa', 'evento', 'churrasqueira', 'salão', 'piscina'],\n  boleto: ['boleto', 'segunda via', 'pagamento', 'cobrança', 'taxa', 'valor', 'vencimento'],\n  manutencao: ['problema', 'quebrou', 'conserto', 'reparo', 'manutenção', 'defeito', 'chamado'],\n  aviso: ['aviso', 'comunicado', 'informar', 'notificar', 'vencimento', 'todos']\n};\n\nlet intencaoDetectada = 'conversar';\nlet confianca = 0;\n\nfor (const [acao, palavras] of Object.entries(intencoes)) {\n  const matches = palavras.filter(palavra => msgLower.includes(palavra)).length;\n  if (matches > confianca) {\n    confianca = matches;\n    intencaoDetectada = acao === 'agendar' ? 'agendar_evento' : \n                       acao === 'boleto' ? 'emitir_boleto' :\n                       acao === 'manutencao' ? 'solicitar_assistencia' :\n                       acao === 'aviso' ? 'enviar_aviso_vencimento' : 'conversar';\n  }\n}\n\n// 4. CONTEXTO ESTRUTURADO PARA IA\nconst contextoIA = {\n  usuario: userData,\n  conhecimento: baseConhecimento,\n  analise: {\n    intencao_detectada: intencaoDetectada,\n    confianca: confianca,\n    palavras_chave: msgLower.split(' ').filter(p => p.length > 2)\n  },\n  \n  exemplos: {\n    agendar_evento: {\n      acao: 'agendar_evento',\n      params: { titulo: 'Festa de aniversário', data: '2025-08-20', horario: '19:00', local: 'Salão de festas' },\n      resposta_usuario: '✅ Perfeito! Vou agendar o salão de festas para você. Que data e horário prefere?'\n    },\n    emitir_boleto: {\n      acao: 'emitir_boleto',\n      params: { competencia: '2025-08', tipo: 'condominio' },\n      resposta_usuario: '📄 Vou gerar a segunda via do seu boleto. Para qual competência (mês/ano)?'\n    },\n    solicitar_assistencia: {\n      acao: 'solicitar_assistencia',\n      params: { titulo: 'Problema elétrico', categoria: 'elétrica', descricao: 'Tomada sem energia' },\n      resposta_usuario: '🔧 Entendi o problema! Vou abrir um chamado de manutenção. Pode detalhar o que está acontecendo?'\n    }\n  }\n};\n\n// 5. PROMPT CONTEXTUALIZADO\nconst promptTreinado = `ASSISTENTE ESPECIALIZADO EM GESTÃO PREDIAL\n\nUSUÁRIO: ${userData.nome} - Unidade ${userData.unidade}\nMENSAGEM: \"${userData.mensagem}\"\n\nANÁLISE PRÉVIA:\n- Intenção detectada: ${intencaoDetectada}\n- Confiança: ${confianca} palavras-chave\n\nBASE DE CONHECIMENTO:\n- Áreas: ${baseConhecimento.areas_comuns.join(', ')}\n- Boletos: ${baseConhecimento.tipos_boleto.join(', ')}\n- Manutenção: ${baseConhecimento.categorias_manutencao.join(', ')}\n\nRESPONDA SEMPRE EM JSON:\n{\n  \"acao\": \"${intencaoDetectada}\",\n  \"params\": { \"detalhes\": \"específicos\" },\n  \"resposta_usuario\": \"mensagem amigável\"\n}\n\nSUA RESPOSTA:`;\n\nconsole.log('Contexto IA preparado:', JSON.stringify(contextoIA, null, 2));\n\nreturn {\n  ...$json,\n  contexto_ia: contextoIA,\n  prompt_treinado: promptTreinado,\n  intencao_detectada: intencaoDetectada,\n  confianca_analise: confianca\n};"}, "id": "preparar-contexto-ia", "name": "Preparar Contexto IA", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [-2900, -400]}, {"parameters": {"functionCode": "// Parse da resposta do agente IA DeepSeek\nlet response = $json.output || $json.text || $json.message || $json.response || '';\n\n// Log para debug\nconsole.log('Resposta do AI Agent:', response);\nconsole.log('Dados recebidos:', JSON.stringify($json, null, 2));\n\n// Tentar extrair JSON da resposta\nlet parsedData = null;\n\ntry {\n  // Primeira tentativa: JSON direto\n  parsedData = JSON.parse(response);\n} catch (e) {\n  try {\n    // Segunda tentativa: encontrar JSON entre ``` ou ```json\n    const jsonMatch = response.match(/```(?:json)?\\s*({[\\s\\S]*?})\\s*```/);\n    if (jsonMatch) {\n      parsedData = JSON.parse(jsonMatch[1]);\n    }\n  } catch (e2) {\n    try {\n      // Terceira tentativa: encontrar qualquer JSON válido\n      const jsonMatch = response.match(/{[\\s\\S]*?}/);\n      if (jsonMatch) {\n        parsedData = JSON.parse(jsonMatch[0]);\n      }\n    } catch (e3) {\n      try {\n        // Quarta tentativa: verificar se a resposta já é um objeto\n        if (typeof response === 'object' && response !== null) {\n          parsedData = response;\n        }\n      } catch (e4) {\n        console.log('Erro no parse:', e4);\n        // Fallback: resposta simples\n        parsedData = {\n          acao: \"conversar\",\n          params: {},\n          resposta_usuario: response || \"Desculpe, não consegui processar sua solicitação.\"\n        };\n      }\n    }\n  }\n}\n\n// Validar estrutura do JSON\nif (!parsedData || typeof parsedData !== 'object') {\n  parsedData = {\n    acao: \"conversar\",\n    params: {},\n    resposta_usuario: response || \"Resposta inválida do agente.\"\n  };\n}\n\n// Garantir que tem os campos obrigatórios\nif (!parsedData.acao) parsedData.acao = \"conversar\";\nif (!parsedData.params) parsedData.params = {};\nif (!parsedData.resposta_usuario) parsedData.resposta_usuario = \"Como posso ajudar você?\";\n\nconsole.log('Dados parseados:', JSON.stringify(parsedData, null, 2));\n\nreturn {\n  ...$json,\n  acao: parsedData.acao,\n  params: parsedData.params,\n  resposta_usuario: parsedData.resposta_usuario\n};"}, "id": "d6928043-780c-4c23-b941-ebcc3f1c6b07", "name": "Parse Ação do Agente1", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [-2480, -240]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "🔗 Seu boleto está pronto: {{$json.data?.boleto_url || $json.data?.link || $json.data?.pdf || 'Link não disponível'}}"}]}, "options": {}}, "id": "eea252c8-b886-446d-bffd-f947255eb10d", "name": "Set Resposta Boleto1", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-1824, -352]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "📣 Avisos de vencimento preparados para {{$json.params?.competencia || 'período não especificado'}}. Total: {{$json.data?.total || $json.data?.count || 0}}"}]}, "options": {}}, "id": "b42896a7-07c9-46c6-83fd-bab2e50d2585", "name": "Set Resposta Aviso1", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-1824, -144]}, {"parameters": {"resource": "Messages", "operation": "Send Text Message", "chatId": "={{$json.normPhone}}@c.us", "text": "={{$json.reply_text || 'Erro no processamento da mensagem.'}}", "requestOptions": {}}, "type": "@devlikeapro/n8n-nodes-waha.WAHA", "typeVersion": 202502, "position": [-1600, -240], "id": "1287d038-7b71-4726-a182-910aecd1a4a8", "name": "Enviar Mensagem WAHA", "credentials": {"wahaApi": {"id": "waha-credential", "name": "WAHA API"}}}], "pinData": {}, "connections": {"AI Agent": {"main": [[{"node": "Parse Ação do Agente1", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Redis Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Switch Ações": {"main": [[{"node": "API Agendar Evento", "type": "main", "index": 0}], [{"node": "API Emitir Boleto", "type": "main", "index": 0}], [{"node": "API Abrir Ticket", "type": "main", "index": 0}], [{"node": "API Preparar Avisos", "type": "main", "index": 0}], [{"node": "Set Resposta Conversa", "type": "main", "index": 0}]]}, "API Agendar Evento": {"main": [[{"node": "Set Resposta Agenda", "type": "main", "index": 0}]]}, "API Emitir Boleto": {"main": [[{"node": "Set Resposta Boleto1", "type": "main", "index": 0}]]}, "API Abrir Ticket": {"main": [[{"node": "Set Resposta Ticket", "type": "main", "index": 0}]]}, "API Preparar Avisos": {"main": [[{"node": "Set Resposta Aviso1", "type": "main", "index": 0}]]}, "Set Resposta Agenda": {"main": [[{"node": "Enviar Mensagem WAHA", "type": "main", "index": 0}]]}, "Set Resposta Ticket": {"main": [[{"node": "Enviar Mensagem WAHA", "type": "main", "index": 0}]]}, "Set Resposta Conversa": {"main": [[{"node": "Enviar Mensagem WAHA", "type": "main", "index": 0}]]}, "Webhook Entrada1": {"main": [[{"node": "Normalizar Telefone1", "type": "main", "index": 0}]]}, "Normalizar Telefone1": {"main": [[{"node": "Check Allowlist1", "type": "main", "index": 0}]]}, "Check Allowlist1": {"main": [[{"node": "Avaliar Autorização1", "type": "main", "index": 0}]]}, "Avaliar Autorização1": {"main": [[{"node": "IF Autorizado?1", "type": "main", "index": 0}]]}, "IF Autorizado?1": {"main": [[{"node": "Preparar Contexto IA", "type": "main", "index": 0}], [{"node": "Enviar Mensagem WAHA", "type": "main", "index": 0}]]}, "Parse Ação do Agente1": {"main": [[{"node": "Switch Ações", "type": "main", "index": 0}]]}, "Set Resposta Boleto1": {"main": [[{"node": "Enviar Mensagem WAHA", "type": "main", "index": 0}]]}, "Set Resposta Aviso1": {"main": [[{"node": "Enviar Mensagem WAHA", "type": "main", "index": 0}]]}, "Preparar Contexto IA": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5d193b95-23d5-4af2-86bf-e09582d3425e", "meta": {"templateCredsSetupCompleted": true, "instanceId": "26bb5e00e3b41650f1c80a17ba0b2bc2c2393bb92f9a42c3ffc7c225c9d35205"}, "id": "LLYI8z1AKEraMYdr", "tags": []}