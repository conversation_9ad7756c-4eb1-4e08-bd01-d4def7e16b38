-- MySQL 8+ DDL para onboarding/allowlist

-- Tabela principal
CREATE TABLE IF NOT EXISTS allowed_phones (
  phone VARCHAR(32) PRIMARY KEY,
  nome VARCHAR(100),
  email VARCHAR(150),
  unidade VARCHAR(20),
  status ENUM('approved','pending','blocked') NOT NULL DEFAULT 'approved',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

-- Log de onboarding
CREATE TABLE IF NOT EXISTS onboarding_requests (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  source VARCHAR(20) NOT NULL DEFAULT 'email',
  raw_from TEXT,
  raw_subject TEXT,
  raw_body LONGTEXT,
  parsed_name VA<PERSON>HA<PERSON>(100),
  parsed_email VARCHAR(150),
  parsed_phone VARCHAR(32),
  parsed_unidade VARCHAR(20),
  status VARCHAR(20) NOT NULL DEFAULT 'received',
  error_msg TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  processed_at TIMESTAMP NULL DEFAULT NULL
) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;

CREATE INDEX idx_allowed_phones_status ON allowed_phones(status);
CREATE INDEX idx_allowed_phones_email ON allowed_phones(email);
