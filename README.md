# AGENTE IA Síndico — Conexão com Supabase e Aprovação de Acessos

Este projeto contém dois fluxos n8n principais:

- `agente_ia_sindico.json` — Atende via Webhook (Evolution API/RAPI) com DeepSeek e executa ações usando Switch para roteamento (agenda, boleto, assistência, avisos). Garante o acesso via allowlist por telefone consultando Postgres/Supabase.
- `onboarding_via_email.json` — Onboarding por e-mail (IMAP) que coleta pedidos de cadastro e grava os telefones na tabela `allowed_phones` do Postgres (Supabase) com `status = 'pending'`. Envia e-mail de confirmação ao solicitante.

Síndicos aprovam/negam acessos alterando o campo `status` de `allowed_phones` para `approved` (aprovado) ou `denied` (negado) diretamente no Supabase (Table Editor) ou via um fluxo admin no n8n.

## Arquitetura do Fluxo Principal

**Entrada:** Webhook recebe dados da Evolution API ou RAPI
**Autorização:** Verifica telefone na allowlist do Postgres/Supabase
**IA:** Agente DeepSeek analisa mensagem e define ação
**Roteamento:** Switch direciona para a ação correta (mais eficiente que múltiplos IFs)
**Saída:** Evolution API envia resposta via WhatsApp

## 1) Requisitos

- n8n (versão recente)
- Conta Supabase (projeto ativo)
- Evolution API ou RAPI configurada
- Credenciais DeepSeek

## 2) Estrutura do banco (Supabase/Postgres)

Tabela principal:

- `allowed_phones`
  - `phone` (PK, varchar) — Telefone normalizado (ex.: 11999999999)
  - `nome`, `email`, `unidade`
  - `status` (varchar) — `pending` | `approved` | `denied`
  - `created_at`, `updated_at`

Observações:
- O fluxo `onboarding_via_email.json` cria/ajusta as tabelas automaticamente e define `status` padrão como `pending`.
- Se preferir criar manualmente, use `sql/postgres_allowlist.sql` como base e aplique o ajuste abaixo para deixar o padrão em `pending`:
  - ALTER TABLE allowed_phones ALTER COLUMN status SET DEFAULT 'pending';

## 3) Conectar o Postgres do n8n ao Supabase

1. No Supabase, pegue os dados de conexão:
   - Project Settings → Database → Connection string.
   - Host (ex.: `db.<PROJECT-REF>.supabase.co`), Port `5432`, Database (geralmente `postgres`), User (ex.: `postgres`), Password.
   - SSL: obrigatório (use "require").

2. No n8n, crie uma credencial Postgres (Credentials → New → Postgres):
   - Host: `db.<PROJECT-REF>.supabase.co`
   - Port: `5432`
   - Database: `postgres` (ou o que você usar)
   - User/Password: do Supabase
   - SSL: Enabled (Require)

3. Edite os nós Postgres dos fluxos para usar essa credencial.
   - Nos arquivos aqui, os nós referenciam a credencial de exemplo `unia_kursar`. Substitua pela sua.

Dica: Para operações de servidor (n8n), você pode usar a credencial Service Role do Supabase. Guarde-a com segurança. Não exponha em clientes.

## 4) Habilitar o fluxo de Onboarding (pendente por padrão)

- Importe/abra `onboarding_via_email.json` no n8n.
- Configure o nó IMAP com sua caixa de entrada dedicada a cadastros.
- Configure o nó Email Send (remetente `SMTP_FROM`).
- Configure os dois nós Postgres com a credencial Supabase criada.
- Ative o fluxo. Ao receber um e-mail, o fluxo:
  1) Extrai nome, telefone, unidade e define `status='pending'`.
  2) Cria as tabelas/trigger (DDL) se não existirem.
  3) Faz UPSERT em `allowed_phones` preservando `approved` em conflitos.
  4) Envia confirmação informando que está “aguardando aprovação”.

## 5) Como os síndicos aprovam/negam acessos

Opção A — Supabase Table Editor (mais rápido):
- No Supabase Studio, vá em Table Editor → `allowed_phones`.
- Filtre por `status = 'pending'`.
- Edite a(s) linha(s) e mude `status` para `approved` (ou `denied`).

Opção B — Fluxo Admin no n8n (sem acesso direto ao banco):
- Crie um fluxo com um Webhook que receba `phone` e `action` (`approve`/`deny`).
- Adicione um nó Postgres com a query:
  - UPDATE allowed_phones SET status = {{$json.action === 'approve' ? 'approved' : 'denied'}}, updated_at = NOW() WHERE phone = '{{$json.phone}}';
- Proteja o Webhook com uma chave secreta/Basic Auth/IP allowlist.
- Entregue a URL apenas aos síndicos.

## 6) Configuração do fluxo principal (WhatsApp via Evolution API)

O fluxo `agente_ia_sindico.json` usa a seguinte arquitetura otimizada:

1. **Webhook Entrada** - Recebe mensagens da Evolution API/RAPI
2. **Normalizar Telefone** - Extrai e normaliza o número do telefone
3. **Check Allowlist** - Consulta Postgres/Supabase para verificar autorização
4. **IF Autorizado** - Gate de segurança
5. **AI Agent (DeepSeek)** - Processa a mensagem e define ação
6. **Parse Ação** - Extrai JSON estruturado da resposta da IA
7. **Switch Ações** - Roteia para a ação correta (substitui múltiplos IFs)
8. **HTTP Requests** - Executa integrações (Calendar, Payments, Tickets, Notifications)
9. **Evolution API Send** - Envia resposta via WhatsApp

### Variáveis de Ambiente Necessárias:

```
# Evolution API/RAPI
EVOLUTION_API_URL=https://sua-evolution-api.com
EVOLUTION_API_KEY=sua-chave-api
EVOLUTION_INSTANCE=sua-instancia

# APIs de Integração
CALENDAR_API_URL=https://sua-api-calendario.com
CALENDAR_API_TOKEN=seu-token-calendario
PAYMENTS_API_URL=https://sua-api-pagamentos.com
PAYMENTS_API_TOKEN=seu-token-pagamentos
TICKETS_API_URL=https://sua-api-tickets.com
TICKETS_API_TOKEN=seu-token-tickets
NOTIFICATIONS_API_URL=https://sua-api-notificacoes.com
NOTIFICATIONS_API_TOKEN=seu-token-notificacoes
```

## 7) Normalização de telefone (importante)

- Use apenas dígitos (remova `+`, espaços, parênteses, traços).
- Para números BR, guarde no formato de 11 dígitos (DDD + número), ex.: `11999999999`.
- O onboarding já normaliza; garanta que o fluxo principal usa a mesma regra ao consultar.

## 8) Boas práticas de segurança

- Use Service Role apenas no servidor (n8n), nunca no cliente.
- Se der acesso ao Supabase Studio a síndicos, crie usuários separados e considere RLS (Row Level Security) com políticas por condomínio.
- Se optar por Webhook admin, proteja-o com autenticação e restrições de IP/segredo.

## 9) Troubleshooting

- Não criou tabelas? Rode o fluxo de onboarding ao menos uma vez ou execute o DDL no SQL Editor do Supabase (ou use `sql/postgres_allowlist.sql` + ajuste do default para `pending`).
- `approved` foi rebaixado para `pending`? O UPSERT do onboarding aqui preserva `approved`. Verifique se está usando esta versão do fluxo.
- Erro de SSL no Postgres: marque SSL como "require" na credencial do n8n.

## 10) Próximos passos (opcionais)

- Criar um pequeno painel (n8n/Retool/Supabase Auth) para aprovação com login.
- Mover a verificação do fluxo principal para Supabase (fonte única de verdade).
- Adicionar auditoria em `onboarding_requests` (o fluxo já cria a tabela; você pode inserir ali o conteúdo bruto do e-mail, se desejar).
