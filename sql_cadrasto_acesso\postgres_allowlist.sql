-- PostgreSQL DDL para onboarding/allowlist

-- Tabela principal
CREATE TABLE IF NOT EXISTS allowed_phones (
  phone VARCHAR(32) PRIMARY KEY,
  nome VARCHAR(100),
  email VARCHAR(150),
  unidade VARCHAR(20),
  status VARCHAR(10) NOT NULL DEFAULT 'approved',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Trigger para updated_at
CREATE OR REPLACE FUNCTION set_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'trg_allowed_phones_updated'
  ) THEN
    CREATE TRIGGER trg_allowed_phones_updated
    BEFORE UPDATE ON allowed_phones
    FOR EACH ROW EXECUTE FUNCTION set_updated_at();
  END IF;
END$$;

-- Log de onboarding
CREATE TABLE IF NOT EXISTS onboarding_requests (
  id BIGSERIAL PRIMARY KEY,
  source VARCHAR(20) NOT NULL DEFAULT 'email',
  raw_from TEXT,
  raw_subject TEXT,
  raw_body TEXT,
  parsed_name VARCHAR(100),
  parsed_email VARCHAR(150),
  parsed_phone VARCHAR(32),
  parsed_unidade VARCHAR(20),
  status VARCHAR(20) NOT NULL DEFAULT 'received',
  error_msg TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  processed_at TIMESTAMPTZ
);

CREATE INDEX IF NOT EXISTS idx_allowed_phones_status ON allowed_phones(status);
CREATE INDEX IF NOT EXISTS idx_allowed_phones_email ON allowed_phones(email);
