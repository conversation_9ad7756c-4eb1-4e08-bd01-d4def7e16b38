# 🔗 Mapeamento de Conexões - Set Resposta Conversa

## ✅ **Conexão Corrigida!**

### 📍 **"Set Resposta Conversa" está conectado a:**

```
┌─────────────────────┐
│   Switch Ações      │
├─────────────────────┤
│ Output 0 → Agendar  │
│ Output 1 → Boleto   │  
│ Output 2 → Ticket   │
│ Output 3 → Avisos   │
│ Output 4 → CONVERSA │ ✅ (fallback)
└─────────────────────┘
        │
        ↓ (Output 4)
┌─────────────────────┐
│ Set Resposta        │
│ Conversa            │
└─────────────────────┘
        │
        ↓
┌─────────────────────┐
│ Enviar Mensagem     │
│ WAHA                │
└─────────────────────┘
```

## 🔄 **Fluxo Completo das Conexões**

### **Switch Ações (5 saídas)**:

1. **Output 0** (`agendar_evento`) → **API Agendar Evento**
2. **Output 1** (`emitir_boleto`) → **API Emitir Boleto**
3. **Output 2** (`solicitar_assistencia`) → **API Abrir Ticket**
4. **Output 3** (`enviar_aviso_vencimento`) → **API Preparar Avisos**
5. **Output 4** (`fallback/conversar`) → **Set Resposta Conversa** ✅

### **Cada API se conecta ao seu Set**:
- **API Agendar** → **Set Resposta Agenda** → **WAHA**
- **API Boleto** → **Set Resposta Boleto** → **WAHA**
- **API Ticket** → **Set Resposta Ticket** → **WAHA**
- **API Avisos** → **Set Resposta Aviso** → **WAHA**

### **Conversa vai direto**:
- **Set Resposta Conversa** → **WAHA** ✅

## 🎯 **Quando o "Set Resposta Conversa" é Usado?**

### **Casos de Fallback**:
1. **Ação não identificada**: `acao: "conversar"`
2. **Conversa geral**: Quando IA não detecta ação específica
3. **Erro de parsing**: Quando JSON não contém ação válida
4. **Interação social**: Cumprimentos, agradecimentos, etc.

### **Exemplo de Uso**:
```javascript
// Entrada: "Oi, tudo bem?"
// Parse retorna: { acao: "conversar", resposta_usuario: "Olá! Como posso ajudar?" }
// Switch não encontra "conversar" → Vai para fallbackOutput (4)
// Output 4 → Set Resposta Conversa → WAHA
```

## 📊 **Configuração do Switch**

```javascript
{
  "fallbackOutput": 4,  // ← Saída para conversas
  "rules": [
    { "value2": "agendar_evento",        "output": 0 },
    { "value2": "emitir_boleto",         "output": 1 },
    { "value2": "solicitar_assistencia", "output": 2 },
    { "value2": "enviar_aviso_vencimento", "output": 3 }
    // Output 4 = fallback (não precisa regra)
  ]
}
```

## ✅ **Verificação das Conexões**

### **Entrada no Set Resposta Conversa**:
- ✅ **Switch Ações** (Output 4 - fallback)

### **Saída do Set Resposta Conversa**:
- ✅ **Enviar Mensagem WAHA**

### **Conteúdo do Set**:
```javascript
{
  "reply_text": "={{$json.resposta_usuario || 'Como posso ajudar você?'}}"
}
```

## 🚀 **Resultado**

**CONEXÃO CORRETA**: 
`Switch Ações` → `Set Resposta Conversa` → `Enviar Mensagem WAHA`

**FUNÇÃO**: Lidar com conversas gerais e fallbacks quando nenhuma ação específica é identificada.

**STATUS**: ✅ **FUNCIONANDO CORRETAMENTE**
