{"name": "Agente IA Síndico - WhatsApp Evolution/RAPI", "nodes": [{"parameters": {"functionCode": "// Extrair o número do telefone do webhook (pode vir em diferentes formatos)\nlet from = $json.body?.remoteJid || $json.body?.from || $json.remoteJid || $json.from || '';\n\n// Remover prefixos e caracteres não numéricos\nfrom = String(from).replace(/^whatsapp:|@s.whatsapp.net$/g, '').replace(/[^0-9+]/g, '');\nlet digits = from.replace(/[^0-9]+/g, '');\n\n// Normalizar para formato brasileiro (11 dígitos)\nif (digits.startsWith('55') && digits.length > 11) {\n  digits = digits.slice(digits.length - 11);\n}\n\n// Extrair mensagem do webhook\nconst message = $json.body?.body || $json.body?.message?.text || $json.body?.text || $json.message || '';\n\nreturn { \n  ...item, \n  normPhone: digits,\n  messageText: message,\n  originalFrom: from\n};"}, "id": "normalizar-telefone", "name": "Normalizar Telefone", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [-1000, 200]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE IF NOT EXISTS allowed_phones (\n  phone VARCHAR(32) PRIMARY KEY,\n  nome VARCHAR(100),\n  email VARCHAR(150),\n  unidade VARCHAR(20),\n  status VARCHAR(10) NOT NULL DEFAULT 'pending',\n  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),\n  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()\n);\n\nDO $$\nBEGIN\n  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_allowed_phones_updated') THEN\n    CREATE OR REPLACE FUNCTION set_updated_at()\n    RETURNS TRIGGER AS $$\n    BEGIN\n      NEW.updated_at = NOW();\n      RETURN NEW;\n    END;\n    $$ LANGUAGE plpgsql;\n\n    CREATE TRIGGER trg_allowed_phones_updated\n    BEFORE UPDATE ON allowed_phones\n    FOR EACH ROW EXECUTE FUNCTION set_updated_at();\n  END IF;\nEND$$;", "options": {}}, "id": "07244d66-3b2d-426b-b136-1d5c41d13e9a", "name": "Postgres - Ensure Table", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-2208, -384], "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT phone, nome, unidade, status FROM allowed_phones WHERE phone='{{$json.normPhone}}' AND status='approved' LIMIT 1;", "options": {}}, "id": "check-allowlist", "name": "Check Allowlist", "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-800, 200], "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"mode": "wait", "options": {}}, "id": "32e668a8-e48a-48df-820a-dfa548fbc685", "name": "Merge <PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 2, "position": [-1152, -528]}, {"parameters": {"functionCode": "// Verificar se o telefone está autorizado\nconst hasData = $json.phone ? true : false;\n\nif (hasData) {\n  return {\n    ...item,\n    allowed: true,\n    nome: $json.nome,\n    unidade: $json.unidade\n  };\n} else {\n  return {\n    ...item,\n    allowed: false,\n    nome: null,\n    unidade: null\n  };\n}"}, "id": "avaliar-auth", "name": "Avaliar Autorização", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [-600, 200]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.allowed}}", "operation": "isTrue"}]}, "options": {}}, "id": "63f86ebf-b1a8-433c-af8e-b1b5a69253af", "name": "IF Autorizado?", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-512, -208]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "Número não autorizado. Envie um e-mail para cadastro contendo seu telefone e unidade."}]}, "options": {}}, "id": "a2a2e1cf-1dd1-486d-8a4c-cc1b7c14fdde", "name": "Set Não Autorizado", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-288, -112]}, {"parameters": {"functionCode": "const raw = $node[\"Agente IA (DeepSeek)\"].json[\"output\"];\nlet data;\ntry {\n  data = JSON.parse(raw);\n} catch (e) {\n  return { ...item, action: 'reply_only', params: {}, reply_text: raw };\n}\nconst action = data.action || 'reply_only';\nconst params = data.params || {};\nconst message = data.message || raw;\nreturn { ...item, action, params, reply_text: message };"}, "id": "a9dd9df4-cf06-46a8-8703-00afe700a447", "name": "Parse Ação do Agente", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [-1584, -384]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.action}}", "operation": "equal", "value2": "agendar_evento"}]}, "options": {}}, "id": "e6e0ee58-5e64-4227-b7ec-f13876333f31", "name": "IF Agendar Evento", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1376, -384]}, {"parameters": {"method": "POST", "url": "={{$env.CALENDAR_API_URL}}/events", "sendHeaders": true, "headerParameters": {"parameters": [{}]}, "options": {}}, "id": "e15f43bc-6988-4a0e-b247-9858996f4fcf", "name": "HTTP Agendar Evento", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-896, -304]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "={{'✅ Evento agendado: ' + ($json.params.titulo || 'Reserva') + ( $json.data && $json.data.link ? ' — ' + $json.data.link : '' )}}"}]}, "options": {}}, "id": "57d64ac4-0645-4e68-b668-a54ac76abb79", "name": "Set Resposta Agendar", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-672, -336]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.action}}", "operation": "equal", "value2": "emitir_boleto"}]}, "options": {}}, "id": "fe70417f-c50b-40c4-904f-ad9b029dd6cd", "name": "IF Emitir Boleto", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1360, -112]}, {"parameters": {"method": "POST", "url": "={{$env.PAYMENTS_API_URL}}/boletos", "sendHeaders": true, "headerParameters": {"parameters": [{}]}, "options": {}}, "id": "28cbed67-2b32-41c6-8026-d1d2df626d1f", "name": "HTTP Emitir Boleto", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-896, -144]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "={{ '🔗 Seu boleto está pronto: ' + ($json.data && ($json.data.boleto_url || $json.data.link || $json.data.pdf)) }}"}]}, "options": {}}, "id": "8d2cf85a-7cc2-4ab0-a38a-c9ad5e42da0e", "name": "Set Resposta Boleto", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-688, -512]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.action}}", "operation": "equal", "value2": "solicitar_assistencia"}]}, "options": {}}, "id": "f77e88e2-c8f4-4552-8271-613369c893ff", "name": "IF Solicitar Assistência", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1344, 144]}, {"parameters": {"method": "POST", "url": "={{$env.TICKETS_API_URL}}/tickets", "sendHeaders": true, "headerParameters": {"parameters": [{}]}, "options": {}}, "id": "25ae8eb7-d477-4fda-ad5c-5e025e130fe9", "name": "HTTP Abrir Ticket", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-896, 16]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "={{ '🛠️ Chamado aberto com sucesso. Protocolo: ' + ($json.data && ($json.data.id || $json.data.protocolo || $json.data.ticket)) }}"}]}, "options": {}}, "id": "19d458d2-2f7a-43d6-bd72-f1f6d1259296", "name": "Set Resposta Assistência", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-672, 16]}, {"parameters": {"conditions": {"string": [{"value1": "={{$json.action}}", "operation": "equal", "value2": "enviar_aviso_vencimento"}]}, "options": {}}, "id": "b865495f-bfb4-4c39-9470-7b40caf9eb61", "name": "IF Aviso de Vencimento", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-1344, 416]}, {"parameters": {"method": "POST", "url": "={{$env.NOTIFICATIONS_API_URL}}/reminders/batch", "sendHeaders": true, "headerParameters": {"parameters": [{}]}, "options": {}}, "id": "9e010acf-4107-4926-91a2-f492a947bc38", "name": "HTTP Preparar Avisos", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4, "position": [-992, 400]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "={{ '📣 Avisos de vencimento preparados para a competência ' + ($json.params && $json.params.competencia ? $json.params.competencia : '') + '. Total: ' + ($json.data && ($json.data.total || $json.data.count || 0)) }}"}]}, "options": {}}, "id": "b6f723ce-5b08-418e-a615-4885df44cfb5", "name": "Set Resposta Aviso", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [-656, 400]}, {"parameters": {}, "id": "bfd56340-febe-4b9f-8ac1-1626f034897c", "name": "Enviar Resposta WhatsApp", "type": "n8n-nodes-base.whatsappBusinessCloud", "typeVersion": 1, "position": [-496, 0], "credentials": {}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.2, "position": [-1888, -384], "id": "7517c673-1439-499e-998d-bc6d7045f063", "name": "AI Agent"}, {"parameters": {"path": "sindico-webhook", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2.1, "position": [-1200, 200], "id": "webhook-entrada", "name": "Webhook Entrada", "webhookId": "sindico-webhook"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "typeVersion": 1, "position": [-2032, -176], "id": "b580e058-1a42-4fce-9baf-03636edcb042", "name": "DeepSeek Chat Model", "credentials": {"deepSeekApi": {"id": "EMVUGRG5w0bLD41Z", "name": "DeepSeek account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryRedisChat", "typeVersion": 1.5, "position": [-1888, -176], "id": "********-1c77-45b1-82af-aac2fc477a45", "name": "Redis <PERSON>", "credentials": {"redis": {"id": "FwBRYM9IJHfqMtD0", "name": "Redis _padrao"}}}], "pinData": {}, "connections": {"Normalizar Telefone": {"main": [[{"node": "Postgres - Ensure Table", "type": "main", "index": 0}]]}, "Postgres - Ensure Table": {"main": [[{"node": "Postgres - Check <PERSON>owlist", "type": "main", "index": 0}]]}, "Postgres - Check Allowlist": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Merge Autorização": {"main": [[{"node": "Avaliar Autorização", "type": "main", "index": 0}]]}, "Avaliar Autorização": {"main": [[{"node": "IF Autorizado?", "type": "main", "index": 0}]]}, "IF Autorizado?": {"main": [[], [{"node": "Set Não Autorizado", "type": "main", "index": 0}]]}, "Parse Ação do Agente": {"main": [[{"node": "IF Agendar Evento", "type": "main", "index": 0}]]}, "IF Agendar Evento": {"main": [[{"node": "HTTP Agendar Evento", "type": "main", "index": 0}], [{"node": "IF Emitir Boleto", "type": "main", "index": 0}]]}, "HTTP Agendar Evento": {"main": [[{"node": "Set Resposta Agendar", "type": "main", "index": 0}]]}, "IF Emitir Boleto": {"main": [[{"node": "HTTP Emitir Boleto", "type": "main", "index": 0}], [{"node": "IF Solicitar Assistência", "type": "main", "index": 0}]]}, "HTTP Emitir Boleto": {"main": [[{"node": "Set Resposta Boleto", "type": "main", "index": 0}]]}, "IF Solicitar Assistência": {"main": [[{"node": "HTTP Abrir Ticket", "type": "main", "index": 0}], [{"node": "IF Aviso de Vencimento", "type": "main", "index": 0}]]}, "HTTP Abrir Ticket": {"main": [[{"node": "Set Resposta Assistência", "type": "main", "index": 0}]]}, "IF Aviso de Vencimento": {"main": [[{"node": "HTTP Preparar Avisos", "type": "main", "index": 0}]]}, "HTTP Preparar Avisos": {"main": [[{"node": "Set Resposta Aviso", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Normalizar Telefone", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Parse Ação do Agente", "type": "main", "index": 0}]]}, "DeepSeek Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Redis Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "c98cd9fb-d814-40ac-a4e2-9cf4b39e5069", "meta": {"templateCredsSetupCompleted": true, "instanceId": "26bb5e00e3b41650f1c80a17ba0b2bc2c2393bb92f9a42c3ffc7c225c9d35205"}, "id": "LLYI8z1AKEraMYdr", "tags": []}