{"name": "Agente IA Síndico - WhatsApp Evolution/RAPI Completo", "nodes": [{"parameters": {"path": "sindico-webhook", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [200, 300], "id": "webhook-entrada", "name": "Webhook Entrada", "webhookId": "sindico-webhook"}, {"parameters": {"functionCode": "// Extrair o número do telefone do webhook (pode vir em diferentes formatos)\nlet from = $json.body?.remoteJid || $json.body?.from || $json.remoteJid || $json.from || '';\n\n// Remover prefixos e caracteres não numéricos\nfrom = String(from).replace(/^whatsapp:|@s.whatsapp.net$/g, '').replace(/[^0-9+]/g, '');\nlet digits = from.replace(/[^0-9]+/g, '');\n\n// Normalizar para formato brasileiro (11 dígitos)\nif (digits.startsWith('55') && digits.length > 11) {\n  digits = digits.slice(digits.length - 11);\n}\n\n// Extrair mensagem do webhook\nconst message = $json.body?.body || $json.body?.message?.text || $json.body?.text || $json.message || '';\n\nreturn { \n  ...$json, \n  normPhone: digits,\n  messageText: message,\n  originalFrom: from\n};"}, "id": "normalizar-telefone", "name": "Normalizar Telefone", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [400, 300]}, {"parameters": {"operation": "execute<PERSON>uery", "query": "SELECT phone, nome, unidade, status FROM allowed_phones WHERE phone='{{$json.normPhone}}' AND status='approved' LIMIT 1;", "options": {}}, "id": "check-allowlist", "name": "Check Allowlist", "type": "n8n-nodes-base.postgres", "typeVersion": 1, "position": [600, 300], "credentials": {"postgres": {"id": "postgres-credential", "name": "PostgreSQL"}}}, {"parameters": {"functionCode": "// Verificar se o telefone está autorizado\nconst hasData = $json.phone ? true : false;\n\nif (hasData) {\n  return {\n    ...$json,\n    allowed: true,\n    nome: $json.nome,\n    unidade: $json.unidade\n  };\n} else {\n  return {\n    ...$json,\n    allowed: false,\n    nome: null,\n    unidade: null,\n    reply_text: 'Número não autorizado. Envie um e-mail para cadastro contendo seu telefone e unidade.'\n  };\n}"}, "id": "avaliar-auth", "name": "Avaliar Autorização", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [800, 300]}, {"parameters": {"conditions": {"boolean": [{"value1": "={{$json.allowed}}", "value2": true}]}}, "id": "if-autorizado", "name": "IF Autorizado?", "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [1000, 300]}, {"parameters": {"promptType": "define", "text": "Você é um assistente virtual especializado em gestão predial para condomínios.\n\nInformações do usuário:\n- Nome: {{$json.nome}}\n- Unidade: {{$json.unidade}}\n- Telefone: {{$json.normPhone}}\n\nMensagem recebida: {{$json.messageText}}\n\nVocê pode realizar as seguintes ações:\n1. agendar_evento - Para reservar áreas comuns, salão de festas, etc.\n2. emitir_boleto - Para segunda via de boletos de condomínio\n3. solicitar_assistencia - Para abrir chamados de manutenção\n4. enviar_aviso_vencimento - Para avisos sobre vencimentos\n\nSEMPRE responda no formato JSON exato:\n{\n  \"acao\": \"nome_da_acao\",\n  \"params\": {\n    \"parametros\": \"específicos da ação\"\n  },\n  \"resposta_usuario\": \"mensagem amigável para o usuário\"\n}\n\nSe não identificar uma ação específica, use acao: \"conversar\" e forneça uma resposta útil."}, "id": "agente-ia", "name": "Agente IA Sindico", "type": "n8n-nodes-base.openAi", "typeVersion": 1, "position": [1200, 300], "credentials": {"openAiApi": {"id": "openai-credential", "name": "OpenAI"}}}, {"parameters": {"functionCode": "// Parse da resposta do agente IA\nlet response = $json.message || $json.text || $json.response || '';\n\n// Tentar extrair JSON da resposta\nlet parsedData = null;\n\ntry {\n  // Primeira tentativa: JSON direto\n  parsedData = JSON.parse(response);\n} catch (e) {\n  try {\n    // Segunda tentativa: encontrar JSON entre ```\n    const jsonMatch = response.match(/```(?:json)?\\s*({[^}]+})\\s*```/);\n    if (jsonMatch) {\n      parsedData = JSON.parse(jsonMatch[1]);\n    }\n  } catch (e2) {\n    try {\n      // Terceira tentativa: encontrar JSON sem delimitadores\n      const jsonMatch = response.match(/{[^}]+}/);\n      if (jsonMatch) {\n        parsedData = JSON.parse(jsonMatch[0]);\n      }\n    } catch (e3) {\n      // Fallback: resposta simples\n      parsedData = {\n        acao: \"conversar\",\n        params: {},\n        resposta_usuario: response || \"<PERSON><PERSON><PERSON><PERSON>, não entendi sua solicitação.\"\n      };\n    }\n  }\n}\n\nreturn {\n  ...$json,\n  acao: parsedData?.acao || \"conversar\",\n  params: parsedData?.params || {},\n  resposta_usuario: parsedData?.resposta_usuario || response\n};"}, "id": "parse-acao", "name": "Parse Ação do Agente", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [1400, 300]}, {"parameters": {"dataType": "string", "value1": "={{$json.acao}}", "rules": {"rules": [{"value2": "agendar_evento", "output": 0}, {"value2": "emitir_boleto", "output": 1}, {"value2": "solicitar_assistencia", "output": 2}, {"value2": "enviar_aviso_vencimento", "output": 3}]}, "fallbackOutput": 4}, "id": "switch-acoes", "name": "Switch Ações", "type": "n8n-nodes-base.switch", "typeVersion": 1, "position": [1600, 300]}, {"parameters": {"method": "POST", "url": "={{$env.CALENDAR_API_URL}}/eventos", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$env.CALENDAR_API_TOKEN}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonParameters": true, "bodyParametersJson": "={\n  \"titulo\": \"{{$json.params.titulo || 'Reserva de ' + $json.nome}}\",\n  \"data\": \"{{$json.params.data}}\",\n  \"horario\": \"{{$json.params.horario}}\",\n  \"local\": \"{{$json.params.local || 'Área comum'}}\",\n  \"responsavel\": \"{{$json.nome}}\",\n  \"unidade\": \"{{$json.unidade}}\",\n  \"telefone\": \"{{$json.normPhone}}\"\n}", "options": {}}, "id": "api-agenda", "name": "API Agendar Evento", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1800, 100]}, {"parameters": {"method": "POST", "url": "={{$env.PAYMENT_API_URL}}/boletos", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$env.PAYMENT_API_TOKEN}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonParameters": true, "bodyParametersJson": "={\n  \"unidade\": \"{{$json.unidade}}\",\n  \"responsavel\": \"{{$json.nome}}\",\n  \"competencia\": \"{{$json.params.competencia}}\",\n  \"tipo\": \"{{$json.params.tipo || 'condominio'}}\",\n  \"telefone\": \"{{$json.normPhone}}\"\n}", "options": {}}, "id": "api-boleto", "name": "API Emitir Boleto", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1800, 200]}, {"parameters": {"method": "POST", "url": "={{$env.TICKET_API_URL}}/chamados", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$env.TICKET_API_TOKEN}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonParameters": true, "bodyParametersJson": "={\n  \"titulo\": \"{{$json.params.titulo || 'Solicitação de assistência'}}\",\n  \"descricao\": \"{{$json.params.descricao || $json.messageText}}\",\n  \"categoria\": \"{{$json.params.categoria || 'manutencao'}}\",\n  \"prioridade\": \"{{$json.params.prioridade || 'media'}}\",\n  \"unidade\": \"{{$json.unidade}}\",\n  \"responsavel\": \"{{$json.nome}}\",\n  \"telefone\": \"{{$json.normPhone}}\"\n}", "options": {}}, "id": "api-ticket", "name": "API Abrir Ticket", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1800, 300]}, {"parameters": {"method": "POST", "url": "={{$env.NOTIFICATION_API_URL}}/avisos", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer {{$env.NOTIFICATION_API_TOKEN}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonParameters": true, "bodyParametersJson": "={\n  \"tipo\": \"vencimento\",\n  \"competencia\": \"{{$json.params.competencia}}\",\n  \"unidades\": \"{{$json.params.unidades || 'todas'}}\",\n  \"responsavel\": \"{{$json.nome}}\",\n  \"telefone\": \"{{$json.normPhone}}\"\n}", "options": {}}, "id": "api-avisos", "name": "API Preparar Avisos", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [1800, 400]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "✅ Evento agendado: {{$json.params.titulo || 'Reserva'}}{{$json.data?.link ? ' - ' + $json.data.link : ''}}"}]}, "options": {}}, "id": "set-agenda", "name": "Set Resposta Agenda", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [2000, 100]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "🔗 Seu boleto está pronto: {{$json.data?.boleto_url || $json.data?.link || $json.data?.pdf || 'Link não disponível'}}"}]}, "options": {}}, "id": "set-boleto", "name": "Set Resposta Boleto", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [2000, 200]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "🛠️ Chamado aberto com sucesso. Protocolo: {{$json.data?.id || $json.data?.protocolo || $json.data?.ticket || 'Não informado'}}"}]}, "options": {}}, "id": "set-ticket", "name": "Set Resposta Ticket", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [2000, 300]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "📣 Avisos de vencimento preparados para {{$json.params?.competencia || 'período não especificado'}}. Total: {{$json.data?.total || $json.data?.count || 0}}"}]}, "options": {}}, "id": "set-aviso", "name": "Set Resposta Aviso", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [2000, 400]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "reply_text", "value": "={{$json.resposta_usuario || 'Como posso ajudar você?'}}"}]}, "options": {}}, "id": "set-conversa", "name": "Set Resposta Conversa", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [2000, 500]}, {"parameters": {"method": "POST", "url": "={{$env.EVOLUTION_API_URL}}/message/sendText/{{$env.EVOLUTION_INSTANCE}}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apikey", "value": "{{$env.EVOLUTION_API_KEY}}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "bodyParameters": {"parameters": []}, "jsonParameters": true, "bodyParametersJson": "={\n  \"number\": \"{{$json.normPhone || $json.originalFrom}}\",\n  \"text\": \"{{$json.reply_text || 'Erro no processamento da mensagem.'}}\"\n}", "options": {}}, "id": "enviar-whatsapp", "name": "Enviar WhatsApp", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [2200, 300]}], "connections": {"Webhook Entrada": {"main": [[{"node": "Normalizar Telefone", "type": "main", "index": 0}]]}, "Normalizar Telefone": {"main": [[{"node": "Check Allowlist", "type": "main", "index": 0}]]}, "Check Allowlist": {"main": [[{"node": "Avaliar Autorização", "type": "main", "index": 0}]]}, "Avaliar Autorização": {"main": [[{"node": "IF Autorizado?", "type": "main", "index": 0}]]}, "IF Autorizado?": {"main": [[{"node": "Agente IA Sindico", "type": "main", "index": 0}], [{"node": "Enviar WhatsApp", "type": "main", "index": 0}]]}, "Agente IA Sindico": {"main": [[{"node": "Parse Ação do Agente", "type": "main", "index": 0}]]}, "Parse Ação do Agente": {"main": [[{"node": "Switch Ações", "type": "main", "index": 0}]]}, "Switch Ações": {"main": [[{"node": "API Agendar Evento", "type": "main", "index": 0}], [{"node": "API Emitir Boleto", "type": "main", "index": 0}], [{"node": "API Abrir Ticket", "type": "main", "index": 0}], [{"node": "API Preparar Avisos", "type": "main", "index": 0}], [{"node": "Set Resposta Conversa", "type": "main", "index": 0}]]}, "API Agendar Evento": {"main": [[{"node": "Set Resposta Agenda", "type": "main", "index": 0}]]}, "API Emitir Boleto": {"main": [[{"node": "Set Resposta Boleto", "type": "main", "index": 0}]]}, "API Abrir Ticket": {"main": [[{"node": "Set Resposta Ticket", "type": "main", "index": 0}]]}, "API Preparar Avisos": {"main": [[{"node": "Set Resposta Aviso", "type": "main", "index": 0}]]}, "Set Resposta Agenda": {"main": [[{"node": "Enviar WhatsApp", "type": "main", "index": 0}]]}, "Set Resposta Boleto": {"main": [[{"node": "Enviar WhatsApp", "type": "main", "index": 0}]]}, "Set Resposta Ticket": {"main": [[{"node": "Enviar WhatsApp", "type": "main", "index": 0}]]}, "Set Resposta Aviso": {"main": [[{"node": "Enviar WhatsApp", "type": "main", "index": 0}]]}, "Set Resposta Conversa": {"main": [[{"node": "Enviar WhatsApp", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"templateCredsSetupCompleted": true}, "pinData": {}, "tags": []}