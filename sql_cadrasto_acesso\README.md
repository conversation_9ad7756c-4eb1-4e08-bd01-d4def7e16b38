# Onboarding/Allowlist para WhatsApp (n8n)

Este diretório contém SQLs para criar as tabelas usadas no onboarding por e-mail e validação de acesso por whitelist no fluxo principal do n8n.

## Arquivos
- sqlite_allowlist.sql: DDL para SQLite (compatível com o fluxo pronto).
- postgres_allowlist.sql: DDL para PostgreSQL.
- mysql_allowlist.sql: DDL para MySQL 8+.

## Campos principais (allowed_phones)
- phone: chave primária (somente dígitos, BR: 11 dígitos).
- nome, email, unidade: metadados do morador.
- status: 'approved' | 'pending' | 'blocked' (default: 'approved').
- created_at, updated_at: timestamps.

## Normalização de telefone
No fluxo principal, normalize o telefone do WhatsApp:
- Remova o prefixo "whatsapp:".
- Remova tudo que não for número.
- Se vier com +55 e sobrar mais que 11 dígitos, mantenha os últimos 11.

## Upsert (exemplos)
- SQLite:
  INSERT INTO allowed_phones (phone, nome, email, unidade, status)
  VALUES (:phone, :nome, :email, :unidade, :status)
  ON CONFLICT(phone) DO UPDATE SET
    nome=excluded.nome,
    email=excluded.email,
    unidade=excluded.unidade,
    status=excluded.status;

- PostgreSQL:
  INSERT INTO allowed_phones (phone, nome, email, unidade, status)
  VALUES ($1, $2, $3, $4, $5)
  ON CONFLICT (phone) DO UPDATE SET
    nome = EXCLUDED.nome,
    email = EXCLUDED.email,
    unidade = EXCLUDED.unidade,
    status = EXCLUDED.status;

- MySQL 8+:
  INSERT INTO allowed_phones (phone, nome, email, unidade, status)
  VALUES (?, ?, ?, ?, ?)
  ON DUPLICATE KEY UPDATE
    nome = VALUES(nome),
    email = VALUES(email),
    unidade = VALUES(unidade),
    status = VALUES(status);

## Consulta de autorização
- SQLite:
  SELECT phone, nome, unidade, status FROM allowed_phones
  WHERE phone = :phone AND status = 'approved' LIMIT 1;

- PostgreSQL:
  SELECT phone, nome, unidade, status FROM allowed_phones
  WHERE phone = $1 AND status = 'approved' LIMIT 1;

- MySQL:
  SELECT phone, nome, unidade, status FROM allowed_phones
  WHERE phone = ? AND status = 'approved' LIMIT 1;

## Integração no n8n
- Fluxo de onboarding por e-mail: `onboarding_via_email.json` (IMAP → parse → upsert → e-mail de confirmação).
- Fluxo principal: `agente_ia_sindico.json` contém a checagem da allowlist antes do agente.
- Configure a credencial do banco (SQLite/Postgres/MySQL) conforme arquivo escolhido.

## Boas práticas
- Use status 'pending' para revisões manuais.
- Logue onboarding em `onboarding_requests` para auditoria e troubleshooting.
- Adicione índices conforme o volume crescer.
