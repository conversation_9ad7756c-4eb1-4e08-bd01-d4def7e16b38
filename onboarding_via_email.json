{"name": "Onboarding Allowlist por E-mail (Supabase - Pending)", "nodes": [{"parameters": {"options": {}}, "type": "n8n-nodes-base.emailReadImap", "typeVersion": 2.1, "position": [-528, 80], "id": "0a7ab0a2-fb13-4eec-a5f2-317cf68176f0", "name": "<PERSON><PERSON> (IMAP)"}, {"parameters": {"jsCode": "const body = $json.text || $json.textHtml || '';\nconst subject = $json.subject || '';\nconst from = ($json.from && ($json.from.address || $json.from)) || '';\nlet nome = (subject.match(/cadastro[:\\-]?\\s*(.*?)(?:\\s*-|$)/i) || [])[1] || '';\nlet unidade = (body.match(/unidade\\s*(\\w+)/i) || subject.match(/unidade\\s*(\\w+)/i) || [])[1] || '';\nlet phoneMatch = (body.match(/\\+?\\d[\\d\\s\\-().]{7,}/g) || []).concat(subject.match(/\\+?\\d[\\d\\s\\-().]{7,}/g) || []);\nlet phoneDigits = phoneMatch.map(x => x.replace(/\\D+/g,'')).find(x => x.length >= 10) || '';\nif (phoneDigits.startsWith('55') && phoneDigits.length > 11) {\n  phoneDigits = phoneDigits.slice(phoneDigits.length - 11);\n}\nif (!nome) {\n  const nameFrom = ($json.from && $json.from.name) || '';\n  nome = nameFrom || '';\n}\nconst email = from;\nconst status = 'pending';\nreturn {\n  ...item,\n  phone: phoneDigits,\n  nome,\n  email,\n  unidade,\n  status,\n};"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-272, 48], "id": "3b7a6af8-1f17-4bf3-ad2c-98639ae13213", "name": "Parse E-mail p/ <PERSON>adastro"}, {"parameters": {"operation": "execute<PERSON>uery", "query": "CREATE TABLE IF NOT EXISTS allowed_phones (\n  phone VARCHAR(32) PRIMARY KEY,\n  nome VARCHAR(100),\n  email VARCHAR(150),\n  unidade VARCHAR(20),\n  status VARCHAR(10) NOT NULL DEFAULT 'pending',\n  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),\n  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()\n);\n\nCREATE TABLE IF NOT EXISTS onboarding_requests (\n  id BIGSERIAL PRIMARY KEY,\n  source VARCHAR(20) NOT NULL DEFAULT 'email',\n  raw_from TEXT,\n  raw_subject TEXT,\n  raw_body TEXT,\n  parsed_name VARCHAR(100),\n  parsed_email VARCHAR(150),\n  parsed_phone VARCHAR(32),\n  parsed_unidade VARCHAR(20),\n  status VARCHAR(20) NOT NULL DEFAULT 'received',\n  error_msg TEXT,\n  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),\n  processed_at TIMESTAMPTZ\n);\n\nDO $$\nBEGIN\n  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_allowed_phones_updated') THEN\n    CREATE OR REPLACE FUNCTION set_updated_at()\n    RETURNS TRIGGER AS $$\n    BEGIN\n      NEW.updated_at = NOW();\n      RETURN NEW;\n    END;\n    $$ LANGUAGE plpgsql;\n\n    CREATE TRIGGER trg_allowed_phones_updated\n    BEFORE UPDATE ON allowed_phones\n    FOR EACH ROW EXECUTE FUNCTION set_updated_at();\n  END IF;\nEND$$;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [-32, -32], "id": "88275f7a-e512-4c00-93c7-5afba660c9b1", "name": "Supabase (Postgres) - DDL", "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"operation": "execute<PERSON>uery", "query": "INSERT INTO allowed_phones (phone, nome, email, unidade, status)\nVALUES ('{{$json.phone}}','{{$json.nome}}','{{$json.email}}','{{$json.unidade}}','pending')\nON CONFLICT (phone) DO UPDATE SET\n  nome = EXCLUDED.nome,\n  email = EXCLUDED.email,\n  unidade = EXCLUDED.unidade,\n  status = CASE WHEN allowed_phones.status = 'approved' THEN 'approved' ELSE 'pending' END;", "options": {}}, "type": "n8n-nodes-base.postgres", "typeVersion": 2.6, "position": [176, -32], "id": "bf09fde5-cf33-432f-967d-fc8cdf92645b", "name": "Supabase (Postgres) - Upsert Pending", "credentials": {"postgres": {"id": "LeWeikUfPDeAUxAS", "name": "unia_kursar"}}}, {"parameters": {"fromEmail": "={{$env.SMTP_FROM}}", "toEmail": "={{$json.email}}", "subject": "Cadastro recebido (aguardando aprovação)", "text": "Olá {{ $json.nome || 'morador(a)' }}, recebemos seu pedido de cadastro e ele está em análise. Assim que for aprovado, você receberá um aviso. Unidade: {{ $json.unidade || '-' }}. Telefone: {{ $json.phone || 'não informado' }}.", "options": {}}, "id": "27222e00-04f9-4384-8bd5-32b358579df5", "name": "Enviar Confirmação", "type": "n8n-nodes-base.emailSend", "typeVersion": 1, "position": [400, -16]}, {"parameters": {"keepOnlySet": true, "values": {"string": [{"name": "result", "value": "={{$json.status}}"}, {"name": "phone", "value": "={{$json.phone}}"}]}, "options": {}}, "id": "cb71c792-9c02-4cc6-8542-9df1cb5db599", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 2, "position": [672, 80]}], "pinData": {}, "connections": {"Email Trigger (IMAP)": {"main": [[{"node": "Parse E-mail p/ <PERSON>adastro", "type": "main", "index": 0}]]}, "Parse E-mail p/ Cadastro": {"main": [[{"node": "Supabase (Postgres) - DDL", "type": "main", "index": 0}]]}, "Supabase (Postgres) - DDL": {"main": [[{"node": "Supabase (Postgres) - Upsert Pending", "type": "main", "index": 0}]]}, "Supabase (Postgres) - Upsert Pending": {"main": [[{"node": "Enviar Confirmação", "type": "main", "index": 0}]]}, "Enviar Confirmação": {"main": [[{"node": "<PERSON><PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "1", "meta": {"instanceId": "26bb5e00e3b41650f1c80a17ba0b2bc2c2393bb92f9a42c3ffc7c225c9d35205"}, "id": "oa7zwTaqKD1s2TeN", "tags": []}